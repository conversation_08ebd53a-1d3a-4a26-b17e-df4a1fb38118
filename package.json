{"name": "lisaparyajfront", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev-host": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@reduxjs/toolkit": "^2.8.2", "antd": "^5.26.7", "async-mutex": "^0.5.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "lodash.debounce": "^4.0.8", "motion": "^12.23.12", "mutex": "^1.0.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^6.22.3", "redux-persist": "^6.0.0", "sass": "^1.89.2", "simplebar-react": "^3.3.2", "sonner": "^2.0.7", "swiper": "^11.2.10", "usehooks-ts": "^3.1.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/lodash": "^4.17.20", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier": "^3.6.2", "react-container-query": "^0.13.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vite-plugin-svgr": "^4.3.0"}}