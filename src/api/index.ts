import {
  type BaseQueryFn,
  createApi,
  type FetchArgs,
  fetchBaseQuery,
  type FetchBaseQueryError,
} from '@reduxjs/toolkit/query';
import { AppLocalStorage } from '../utils/index.ts';
import { ACCESS_TOKEN_NAME, REFRESH_TOKEN_NAME } from '../utils/constants.ts';

import { Mutex } from 'async-mutex';

export const AuthURI = {
  LOG_IN: '',
  END_USER_LOG_IN: '',
  END_USER_UPDATE_ACCESS_TOKEN: '',
  UPDATE_ACCESS_TOKEN: '',
  RESET_PASSWORD_INITIATING: '',
  RESET_PASSWORD: '',
  RESTORE_PASSWORD: '',
  CONFIRM_RESET_PASSWORD: '',
  END_USER_SIGN_UP: '',
  RESEND_MAIL: '',
  EMAIL_APPROVE: '',
  ACCOUNT: '',
  SET_PASSWORD: '',
};

const BASE_URL = '';
const PRIVATE_LOCAL_API_BASE_URL = `${BASE_URL}/api`;

const checkShouldUpdateAccessToken = (
  error: FetchBaseQueryError | undefined,
  args: string | FetchArgs,
) => {
  if (typeof args != 'string' && 'url' in args) {
    return error?.status === 401;
  }
};

const baseQuery = fetchBaseQuery({
  baseUrl: PRIVATE_LOCAL_API_BASE_URL,
  prepareHeaders: (headers) => {
    const accessToken = AppLocalStorage.getItem({ key: ACCESS_TOKEN_NAME });

    if (accessToken) {
      headers.set('Authorization', `Bearer ${accessToken}`);
    }
    return headers;
  },
});

const mutex = new Mutex();

const baseQueryWithReauth: BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError> = async (
  args,
  api,
  extraOptions,
) => {
  await mutex.waitForUnlock();

  let result = await baseQuery(args, api, extraOptions);

  const shouldUpdateAccessToken = checkShouldUpdateAccessToken(result?.error, args);

  if (shouldUpdateAccessToken) {
    if (!mutex.isLocked()) {
      const release = await mutex.acquire();

      const refreshToken = AppLocalStorage.getItem({ key: REFRESH_TOKEN_NAME });

      try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { data }: any = await baseQuery(
          {
            method: 'POST',
            url: AuthURI.UPDATE_ACCESS_TOKEN,
            body: { refreshToken },
          },
          api,
          extraOptions,
        );

        if (data && 'accessToken' in data) {
          AppLocalStorage.setItem(ACCESS_TOKEN_NAME, data?.accessToken);

          result = await baseQuery(args, api, extraOptions);
        } else {
          // appLogout(api.dispatch);
        }
      } finally {
        release();
      }
    } else {
      await mutex.waitForUnlock();
      result = await baseQuery(args, api, extraOptions);
    }
  }
  return result;
};

export const api = createApi({
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),
  // tagTypes: TagsArray,
});
