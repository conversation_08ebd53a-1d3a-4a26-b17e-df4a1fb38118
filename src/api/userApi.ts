import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { AppLocalStorage } from '../utils';
import { ACCESS_TOKEN_NAME } from '../utils/constants';
import type { User } from './authApi';

interface UpdateProfileRequest {
  name: string;
  surname: string;
  username: string;
  birthday: string;
}

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface ChangePasswordResponse {
  message: string;
}

interface NotificationSettings {
  emailMarketing: boolean;
  bonusNotifications: boolean;
  winNotifications: boolean;
  depositNotifications: boolean;
  withdrawalNotifications: boolean;
}

type UpdateNotificationSettingsRequest = NotificationSettings;

interface UpdateNotificationSettingsResponse {
  message: string;
  settings: NotificationSettings;
}

// User API slice - manages user profile, settings, and account operations
export const userApi = createApi({
  reducerPath: 'userApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
    prepareHeaders: (headers) => {
      // TODO: Real token will be added later
      const token = AppLocalStorage.getItem({ key: ACCESS_TOKEN_NAME });
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['NotificationSettings'],
  endpoints: (builder) => ({
    updateProfile: builder.mutation<User, UpdateProfileRequest>({
      query: (data) => ({
        url: '/auth/profile',
        method: 'PUT',
        body: data,
      }),
    }),

    changePassword: builder.mutation<ChangePasswordResponse, ChangePasswordRequest>({
      query: (data) => ({
        url: '/auth/change-password',
        method: 'POST',
        body: data,
      }),
    }),

    getNotificationSettings: builder.query<NotificationSettings, void>({
      query: () => ({
        url: '/user/notification-settings',
        method: 'GET',
      }),
      providesTags: ['NotificationSettings'],
    }),

    updateNotificationSettings: builder.mutation<
      UpdateNotificationSettingsResponse,
      UpdateNotificationSettingsRequest
    >({
      query: (data) => ({
        url: '/user/notification-settings',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['NotificationSettings'],
    }),
  }),
});

export const {
  useUpdateProfileMutation,
  useChangePasswordMutation,
  useGetNotificationSettingsQuery,
  useUpdateNotificationSettingsMutation,
} = userApi;

export type { NotificationSettings };
