import styles from '../SidebarNavigation/index.module.scss';
import { Menu } from 'antd';
import { Link } from 'react-router-dom';
import { ACCOUNT_NAV_MENU_ITEMS } from '../../../utils/constants';

export const AccountNavigation = () => {
  const accountNavMenuItems = ACCOUNT_NAV_MENU_ITEMS.map((item) => {
    if (item.isAction) {
      return {
        key: item.key,
        icon: <item.icon />,
        label: item.label,
      };
    }

    return {
      key: item.key,
      icon: <item.icon />,
      label: <Link to={item.path!}>{item.label}</Link>,
    };
  });

  return (
    <div className={styles.container}>
      <Menu
        mode="vertical"
        items={accountNavMenuItems}
        theme="dark"
        defaultSelectedKeys={['sports']}
        className={styles.sidebarNavMenu}
      />
    </div>
  );
};
