@use '../../../styles/media' as *;

.container {
  display: flex;
  position: relative;

  & .mainWrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0;

    @include desktop {
      padding-left: 280px;
    }

    @include laptop {
      padding-left: 280px;
    }
  }

  & .contentWrapper {
    display: flex;
    width: 100%;
    max-width: 100%;
  }

  & .mainContent {
    flex: 1;
    min-width: 0;
    padding-top: 24px;
    padding-bottom: 24px;
    background: var(--neutral-700);

    @include mobile {
      padding-top: 12px;
    }
  }

  & .yellowCircle {
    position: fixed;
    right: 0;
    bottom: -40px;
    width: 200px;
    height: 200px;
    background: var(--primary-500, rgb(213, 255, 0));
    border-radius: 50%;
    filter: blur(150px);
    opacity: 0.9;
    pointer-events: none;
    z-index: 1;
  }
}
