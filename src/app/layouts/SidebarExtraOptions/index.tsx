import { Select } from 'antd';
import { ODDS_FORMAT_OPTIONS } from '../../../utils/constants';
import NoteIcon from '../../../assets/icons/note.svg?react';

import styles from './index.module.scss';

export const SidebarExtraOptions = () => {
  return (
    <div className={styles.container}>
      <Select
        options={ODDS_FORMAT_OPTIONS}
        defaultValue="decimal"
        prefix={
          <div className={styles.prefixContainer}>
            <NoteIcon />

            <span className={styles.prefixText}>Odds:</span>
          </div>
        }
      />
    </div>
  );
};
