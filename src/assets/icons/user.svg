<svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="35" fill="#D5FF00" fill-opacity="0.2"/>
<path d="M48.0854 52.1727C50.6049 52.1727 52.6473 50.1303 52.6473 47.6108C52.6473 45.0913 50.6049 43.0488 48.0854 43.0488C45.5659 43.0488 43.5234 45.0913 43.5234 47.6108C43.5234 50.1303 45.5659 52.1727 48.0854 52.1727Z" fill="white"/>
<g filter="url(#filter0_i_228_44130)">
<path d="M35.4565 33.0772C31.5423 33.0772 28.3398 29.8747 28.3398 25.9605V23.4082C28.3398 19.494 31.5423 16.2915 35.4565 16.2915C39.3708 16.2915 42.5732 19.494 42.5732 23.4082V25.9605C42.5732 29.8747 39.3708 33.0772 35.4565 33.0772Z" fill="#D5FF00"/>
</g>
<g filter="url(#filter1_i_228_44130)">
<path d="M40.1229 47.7229C40.1229 43.4786 43.436 40.0171 47.6154 39.7598C45.9608 36.7573 42.9761 34.5792 39.4631 34.0549C38.2591 34.6781 36.8984 35.0373 35.4558 35.0373C34.0131 35.0373 32.6524 34.6785 31.4484 34.0549C26.0678 34.8581 21.9062 39.527 21.9062 45.122V52.4836C21.9062 53.1571 22.4575 53.7084 23.131 53.7084H42.8335C41.1747 52.2446 40.1229 50.1089 40.1229 47.7229Z" fill="#D5FF00"/>
</g>
<path d="M48.1105 41.7375C44.8049 41.7375 42.125 44.4174 42.125 47.723C42.125 51.0286 44.8049 53.7085 48.1105 53.7085C51.416 53.7085 54.0959 51.0286 54.0959 47.723C54.0959 44.4174 51.416 41.7375 48.1105 41.7375ZM51.1999 48.014L50.0665 49.1473C50.0624 49.1511 50.0574 49.1523 50.0532 49.1561L47.5642 51.6451C47.4045 51.8048 47.1459 51.8048 46.9867 51.6451L45.8534 50.5118C45.6937 50.3522 45.6937 50.0936 45.8534 49.9344L48.0643 47.7234L45.8534 45.5125C45.6937 45.3529 45.6937 45.0943 45.8534 44.935L46.9867 43.8017C47.1464 43.6421 47.4049 43.6421 47.5642 43.8017L50.0532 46.2908C50.0574 46.2945 50.0624 46.2958 50.0661 46.2995L51.1994 47.4328C51.2797 47.5131 51.3192 47.6187 51.3188 47.7238C51.3196 47.8282 51.2801 47.9338 51.1999 48.014Z" fill="url(#paint0_linear_228_44130)"/>
<defs>
<filter id="filter0_i_228_44130" x="28.3398" y="16.2915" width="14.2344" height="16.7856" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_228_44130"/>
</filter>
<filter id="filter1_i_228_44130" x="21.9062" y="34.0549" width="25.7109" height="19.6534" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_228_44130"/>
</filter>
<linearGradient id="paint0_linear_228_44130" x1="48.1105" y1="41.7375" x2="48.1105" y2="53.7085" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC47E"/>
<stop offset="1" stop-color="#FF4C25"/>
</linearGradient>
</defs>
</svg>
