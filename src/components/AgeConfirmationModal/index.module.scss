@use '../../styles/media' as *;

.ageConfirmationWrapper {
  padding: 65px 60px;

  :global(.ant-modal-content) {
    padding: 0;
  }

  @include mobile {
    padding: 15px;
  }

  .header {
    text-align: center;
    margin-bottom: 45px;

    p {
      text-align: left;
    }

    .title {
      font-size: var(--font-size-xxl);
      margin-top: 7px;
      color: var(--white);
      font-weight: var(--font-weight-bold);
    }

    .text {
      color: var(--neutral-300);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-semibold);
    }
  }

  .content {
    margin-bottom: 45px;

    p,
    li {
      color: var(--neutral-50);
      font-size: var(--font-size-sm);
    }

    .confirmationList {
      padding: 0 0 0 15px;
      margin: 0 0 20px 0;

      li {
        list-style: disc;
        list-style-position: outside;
        padding-bottom: 5px;
        font-size: var(--font-size-xxs);

        span {
          font-size: var(--font-size-sm);
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      .link {
        color: var(--primary-500);
        text-decoration: underline;

        &:hover {
          color: var(--primary-400);
        }
      }
    }
  }

  .actions {
    display: flex;
    gap: 15px;
    justify-content: space-between;

    .confirmButton {
      font-weight: var(--font-weight-bold);
    }

    .leaveButton {
      background: var(--neutral-600);
      border-color: var(--neutral-500);
      color: var(--neutral-300);

      &:hover {
        background: var(--neutral-500);
        border-color: var(--neutral-400);
        color: var(--white);
      }
    }
  }
}
