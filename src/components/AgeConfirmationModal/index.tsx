import { <PERSON><PERSON>, Button } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useLocalStorage } from 'usehooks-ts';
import { CloseOutlined } from '@ant-design/icons';
import { AGE_CONFIRMATION_CONFIG } from '../../utils/constants';
import styles from './index.module.scss';
import { LuArrowRight } from 'react-icons/lu';
import clsx from 'clsx';

const AGE_CONFIRMATION_KEY = AGE_CONFIRMATION_CONFIG.STORAGE_KEY;

export const AgeConfirmationModal = () => {
  const [ageConfirmationAccepted, setAgeConfirmationAccepted] = useLocalStorage(
    AGE_CONFIRMATION_KEY,
    false,
  );
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (!ageConfirmationAccepted) {
      setIsModalOpen(true);
    }
  }, [ageConfirmationAccepted]);

  const handleConfirm = useCallback(() => {
    setAgeConfirmationAccepted(true);
    setIsModalOpen(false);
  }, [setAgeConfirmationAccepted]);

  const handleCancel = useCallback(() => {
    setAgeConfirmationAccepted(true);
    setIsModalOpen(false);
  }, [setAgeConfirmationAccepted]);

  const handleLeaveSite = useCallback(() => {
    window.history.back();
  }, []);

  return (
    <Modal
      open={isModalOpen}
      onCancel={handleCancel}
      footer={null}
      centered
      transitionName=""
      maskTransitionName=""
      className={clsx(styles.ageConfirmation, 'ageConfirmation')}
      closeIcon={<CloseOutlined />}
      maskClosable={false}
    >
      <div className={styles.ageConfirmationWrapper}>
        <div className={styles.header}>
          <p className={styles.text}>Age Verification</p>
          <p className={styles.title}>Confirm Age & Accept Rules</p>
        </div>

        <div className={styles.content}>
          <p> Welcome! Before proceeding, please confirm:</p>
          <ul className={styles.confirmationList}>
            <li>
              <span>
                You are {AGE_CONFIRMATION_CONFIG.MIN_AGE}+ (or meet the legal gambling age in your
                country).
              </span>
            </li>
            <li>
              <span>
                You have read and accepted our{' '}
                <a
                  href={AGE_CONFIRMATION_CONFIG.LINKS.TERMS}
                  className={styles.link}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Terms & Conditions
                </a>
                ,{' '}
                <a
                  href={AGE_CONFIRMATION_CONFIG.LINKS.PRIVACY}
                  className={styles.link}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Privacy Policy
                </a>
                , and{' '}
                <a
                  href={AGE_CONFIRMATION_CONFIG.LINKS.RESPONSIBLE_GAMING}
                  className={styles.link}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Responsible Gaming Guidelines
                </a>
                .
              </span>
            </li>
            <li>
              <span>Gambling may involve financial risk. Please play responsibly.</span>
            </li>
          </ul>

          <p>By clicking "Confirm", you confirm compliance.</p>
        </div>

        <div className={styles.actions}>
          <Button type="default" onClick={handleLeaveSite} className={styles.leaveButton}>
            Leave Site
          </Button>
          <Button type="primary" onClick={handleConfirm} className={styles.confirmButton}>
            Confirm
            <LuArrowRight size={20} />
          </Button>
        </div>
      </div>
    </Modal>
  );
};
