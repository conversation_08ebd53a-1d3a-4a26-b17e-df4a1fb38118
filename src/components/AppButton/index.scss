.ant-btn {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  line-height: 20px;
  height: 37px;

  &.primary {
    background-color: var(--primary-500);
    color: var(--neutral-900);
    box-shadow: 0 3px 0 var(--primary-button-shadow);

    &:hover {
      background-color: var(--primary-400);
      box-shadow: 0 3px 0 var(--primary-button-shadow-hover);
    }

    &:active {
      background-color: var(--primary-600);
      box-shadow: none;
    }

    &[disabled],
    &.ant-btn-disabled {
      background-color: var(--neutral-500);
      color: var(--neutral-400);
      box-shadow: none;
      height: 40px;

      &:hover {
        background-color: var(--neutral-500);
        color: var(--neutral-400);
        box-shadow: none;
      }
    }
  }

  &.secondary {
    background-color: var(--secondary-500);
    color: var(--white);
    box-shadow: 0 3px 0 var(--secondary-button-shadow);

    &:hover {
      background-color: var(--secondary-400);
      box-shadow: 0 3px 0 var(--secondary-button-shadow-hover);
    }

    &:active {
      background-color: var(--secondary-600);
      box-shadow: none;
      color: var(--neutral-900)
    }

    &[disabled],
    &.ant-btn-disabled {
      background-color: var(--secondary-900);
      box-shadow: none;
      color: var(--neutral-400);
      height: 40px;
    }
  }

  &.outlined {
    background-color: transparent;
    color: var(--white2);
    border: 1px solid var(--primary-500);
    height: 40px;

    &:hover {
      border-color: var(--primary-400);
    }

    &:active {
      border-color: var(--primary-600);
      color: var(--neutral-50);
    }

    &[disabled] {
      border-color: var(--neutral-500);
    }
  }

  &.default {
    display: flex;
    gap: 8px;
    padding: 0 8px;
    height: 40px;
    min-width: 40px;
    background-color: var(--neutral-500);
    color: var(--white2);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
  }

  &.link {
    color: var(--primary-500);
    background-color: transparent;
    padding: 0;

    &:hover:not(:disabled) {
      color: var(--primary-600);
    }

    &:disabled {
      color: var(--neutral-400);
    }
  }
}
