import { Button, type ButtonProps } from 'antd';
import clsx from 'clsx';
import type { FC } from 'react';

import './index.scss';

type Variant = 'primary' | 'secondary' | 'outlined' | 'default' | 'link';
type Props = Omit<ButtonProps, 'variant'> & {
  variant?: Variant;
};

export const AppButton: FC<Props> = ({
  children,
  variant = 'primary',
  className,
  disabled,
  ...props
}) => {
  return (
    <Button {...props} disabled={disabled} className={clsx(variant, className)}>
      {children}
    </Button>
  );
};
