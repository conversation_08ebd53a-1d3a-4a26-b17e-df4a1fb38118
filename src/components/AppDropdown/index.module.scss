.wrapper {
  position: relative;
  font-size: 14px;
  min-width: 110px;
}

.trigger {
  width: 100%;
  padding: 10px 12px;
  background: var(--neutral-600);
  color: var(--white);
  border: none;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;

  .selected {
    font-weight: 600;
  }
}

.icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
  margin-left: 32px;
  transform: rotate(180deg);

  &.opened {
    transform: rotate(360deg);
  }
}

.options {
  position: absolute;
  top: calc(100% + 4px);
  width: 100%;
  background: var(--neutral-700);
  border-radius: 6px;
  box-shadow: 0 4px 12px var(--black-shadow);
  list-style: none;
  padding: 6px 0;
  z-index: 10;

  .option {
    padding: 10px 14px;
    color: var(--white);
    cursor: pointer;
    transition: background 0.2s;

    &:hover {
      background: var(--neutral-600);
    }

    &.active {
      color: var(--primary-500);
      font-weight: 600;
    }
  }
}
