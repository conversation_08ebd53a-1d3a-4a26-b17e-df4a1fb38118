import { useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';

import clsx from 'clsx';
import ChevronDownIcon from '../../assets/icons/chevron-up.svg?react';

import styles from './index.module.scss';

export type DropdownOption = {
  label: string;
  value: string;
};

type Props = {
  label?: string;
  options: DropdownOption[];
  selected: string;
  onChange: (value: string) => void;
};

export const AppDropdown = ({ label, options, selected, onChange }: Props) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOpen = () => setIsOpen((prev) => !prev);
  const handleSelect = (value: string) => {
    onChange(value);
    setIsOpen(false);
  };

  return (
    <div className={styles.wrapper}>
      <button className={styles.trigger} onClick={toggleOpen}>
        <span>
          {label && <span>{label + ': '} </span>}
          <span className={styles.selected}>
            {options.find((opt) => opt.value === selected)?.label}
          </span>
        </span>
        <ChevronDownIcon
          className={clsx(styles.icon, {
            [styles.opened]: isOpen,
          })}
        />
      </button>

      <AnimatePresence initial={false}>
        {isOpen && (
          <motion.ul
            className={styles.options}
            initial={{ opacity: 0, y: -4 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -4 }}
            transition={{ duration: 0.2 }}
          >
            {options.map((opt) => (
              <li
                key={opt.value}
                className={clsx(styles.option, {
                  [styles.active]: selected === opt.value,
                })}
                onClick={() => handleSelect(opt.value)}
              >
                {opt.label}
              </li>
            ))}
          </motion.ul>
        )}
      </AnimatePresence>
    </div>
  );
};
