.odd {
  position: relative;
  flex: 1;
  height: 38px;
  width: 86.47px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: var(--neutral-700);
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--white);
  border: 2px solid  var(--neutral-700);
  transition: 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: var(--secondary-500);
    background: var(--neutral-500);
  }
}

.oddActive {
  background: var(--secondary-500);
  border-color: var(--secondary-500);

  &:hover {
    background: var(--secondary-500);
    border-color: var(--secondary-500);
  }
}

.locked {
  pointer-events: none;
}

.directionUpIcon {
  position: absolute;
  top: 7px;
  right: 5.5px;
}

.directionDownIcon {
  position: absolute;
  bottom: 6px;
  right: 5.5px;
}