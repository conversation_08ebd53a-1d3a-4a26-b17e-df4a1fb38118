import { type FC, useMemo } from 'react';
import type { BetOddT } from '../../types/entities.ts';

import LockIcon from '../../assets/icons/lock_24px.svg?react';
import RedTriangleDownIcon from '../../assets/icons/red-triangle-down.svg?react';
import GreenTriangleUpIcon from '../../assets/icons/green-triangle-up.svg?react';

import styles from './index.module.scss';
import clsx from 'clsx';

type Props = BetOddT & {
  onClick?: () => void;
  active?: boolean;
};

export const BetOdd: FC<Props> = ({ onClick, direction, value, isLocked, active }) => {
  const memoizedInnerContent = useMemo(() => {
    if (isLocked) return <LockIcon />;
    return <span>{value}</span>;
  }, [isLocked, value]);

  return (
    <button
      onClick={onClick}
      className={clsx(styles.odd, { [styles.oddActive]: active, [styles.locked]: isLocked })}
    >
      {direction === 'UP' && <GreenTriangleUpIcon className={styles.directionUpIcon} />}
      {direction === 'DOWN' && <RedTriangleDownIcon className={styles.directionDownIcon} />}
      {memoizedInnerContent}
    </button>
  );
};
