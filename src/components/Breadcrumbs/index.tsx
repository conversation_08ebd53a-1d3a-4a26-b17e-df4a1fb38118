import { Breadcrumb } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import { HomeOutlined } from '@ant-design/icons';
import { ROUTER_PATHS, BREADCRUMB_LABELS } from '../../utils/constants';
import styles from './index.module.scss';

export const Breadcrumbs = () => {
  const location = useLocation();
  const pathSegments = location.pathname.split('/').filter(Boolean);

  const breadcrumbItems = [
    {
      title: (
        <Link to={ROUTER_PATHS.SPORTS} className={styles.breadcrumbLink}>
          <HomeOutlined />
        </Link>
      ),
    },
  ];

  let currentPath = '';
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const isLast = index === pathSegments.length - 1;
    const label = BREADCRUMB_LABELS[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);

    breadcrumbItems.push({
      title: isLast ? (
        <span className={styles.currentPage}>{label}</span>
      ) : (
        <Link to={currentPath} className={styles.breadcrumbLink}>
          {label}
        </Link>
      ),
    });
  });

  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <div className={styles.breadcrumbsContainer}>
      <Breadcrumb separator="/" items={breadcrumbItems} className={styles.breadcrumbs} />
    </div>
  );
};
