@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 20px;
  background: linear-gradient(135deg, var(--neutral-30) 0%, var(--neutral-100) 100%);
}

.card {
  max-width: 600px;
  width: 100%;
  border-radius: 16px;
  box-shadow: 0 10px 30px var(--black-shadow);
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.cardBody {
  padding: 40px 32px;
  text-align: center;
}

.content {
  margin-bottom: 24px;
}

.iconContainer {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--error), var(--secondary-500));
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  animation: pulse 2s infinite;
}

.icon {
  font-size: var(--font-size-xxl);
  color: var(--white);
}

.title {
  font-size: var(--font-size-heading);
  font-weight: var(--font-weight-semibold);
  color: var(--neutral-700);
  margin: 0 0 16px;
  font-family: 'Poppins', sans-serif;
}

.description {
  font-size: var(--font-size-md);
  color: var(--neutral-300);
  margin: 0 0 32px;
  font-family: 'Poppins', sans-serif;
}

.buttonSpace {
  justify-content: center;
}

.primaryButton {
  border-radius: 8px;
  height: 48px;
  padding-left: 24px;
  padding-right: 24px;
  background: linear-gradient(135deg, var(--info) 0%, var(--info-2) 100%);
  border: none;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-md);
  box-shadow: 0 4px 15px rgba(101, 155, 255, 0.4);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(101, 155, 255, 0.6);
    background: linear-gradient(135deg, var(--info) 0%, var(--info-2) 100%);
  }
}

.secondaryButton {
  border-radius: 8px;
  height: 48px;
  padding-left: 24px;
  padding-right: 24px;
  border: 2px solid var(--neutral-200);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-md);
  color: var(--neutral-400);
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--info);
    color: var(--info);
    transform: translateY(-2px);
  }
}

.errorCard {
  margin-top: 32px;
  border-radius: 12px;
  border: 1px solid var(--red-border);
  background: linear-gradient(135deg, rgba(240, 81, 81, 0.1) 0%, rgba(240, 81, 81, 0.05) 100%);
}

.errorCardBody {
  padding: 20px;
  text-align: left;
}

.errorTitle {
  color: var(--error-2);
  font-weight: var(--font-weight-semibold);
  margin-bottom: 12px;
  font-size: var(--font-size-sm);
  font-family: 'Poppins', sans-serif;
}

.errorDetails {
  font-size: var(--font-size-xs);
  font-family: 'JetBrains Mono', 'Monaco', monospace;
  color: var(--error);
  background: rgba(255, 255, 255, 0.8);
  padding: 16px;
  border-radius: 8px;
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
  border: 1px solid var(--red-border);
}
