import type { FallbackProps } from 'react-error-boundary';
import { <PERSON><PERSON>, Card, Space } from 'antd';
import { ReloadOutlined, BugOutlined } from '@ant-design/icons';
import styles from './index.module.scss';

export const ErrorFallback = ({ error, resetErrorBoundary }: FallbackProps) => {
  return (
    <div className={styles.container}>
      <Card className={styles.card} bodyStyle={{ padding: 0 }}>
        <div className={styles.cardBody}>
          <div className={styles.content}>
            <div className={styles.iconContainer}>
              <BugOutlined className={styles.icon} />
            </div>

            <h2 className={styles.title}>Oops! Something went wrong</h2>

            <p className={styles.description}>
              We encountered an unexpected error. Don&apos;t worry, our team has been notified and
              we&apos;re working to fix it. Please try again or reload the page.
            </p>
          </div>

          <Space size="middle" wrap className={styles.buttonSpace}>
            <Button
              type="primary"
              size="large"
              icon={<ReloadOutlined />}
              onClick={resetErrorBoundary}
              className={styles.primaryButton}
            >
              Try Again
            </Button>

            <Button
              size="large"
              onClick={() => window.location.reload()}
              className={styles.secondaryButton}
            >
              Reload Page
            </Button>
          </Space>

          {import.meta.env.DEV && (
            <Card className={styles.errorCard} bodyStyle={{ padding: 0 }}>
              <div className={styles.errorCardBody}>
                <h4 className={styles.errorTitle}>Development Error Details</h4>
                <pre className={styles.errorDetails}>
                  {error.message}
                  {error.stack && '\n\nStack trace:\n' + error.stack}
                </pre>
              </div>
            </Card>
          )}
        </div>
      </Card>
    </div>
  );
};
