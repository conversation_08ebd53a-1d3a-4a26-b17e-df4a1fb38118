import { useState } from 'react';
import { type GameLeague, SportType } from '../../types/entities.ts';
import { SPORT_ICON_MAP } from '../../utils/constants.ts';

import { motion, AnimatePresence } from 'framer-motion';

import styles from './index.module.scss';

import ChevronUpIcon from '../../assets/icons/chevron-up.svg?react';
import { GameRow } from '../GameRow';
import clsx from 'clsx';
import { ODD_GROUPS_MAP, ODD_TYPE_TITLES } from '../../utils/constants.ts';

type Props = GameLeague & {
  sportType: SportType;
  isMobile: boolean;
  isTablet: boolean;
  supportsGroupedOdds: boolean;
  selectedOddGroup: string;
};

export const GameLeagueSection = ({
  leagueName,
  gameCount,
  games,
  sportType,
  supportsGroupedOdds,
  selectedOddGroup,
  isTablet,
  isMobile,
}: Props) => {
  const [isOpen, setIsOpen] = useState(true);

  const toggleOpen = () => {
    setIsOpen((prev) => !prev);
  };

  const sportTypeIcon = SPORT_ICON_MAP[sportType];

  const oddsList =
    !isMobile && !isTablet ? ODD_GROUPS_MAP.ALL : (ODD_GROUPS_MAP[selectedOddGroup] ?? []);

  return (
    <div className={styles.container}>
      <div className={clsx(styles.header, { [styles.headerNotExpanded]: !isOpen })}>
        <div className={styles.row}>
          <img className={styles.headerSportTypeImage} src={sportTypeIcon} alt={sportType} />
          <h2 className={styles.leagueTitle}>{leagueName}</h2>
          {!!gameCount && <span className={styles.gameCount}>{gameCount}</span>}
        </div>

        <div className={styles.row}>
          <AnimatePresence>
            {isOpen && (
              <motion.div
                key="odds-labels"
                className={styles.oddsLabelsContainer}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                {oddsList.map((oddType) => (
                  <div className={styles.oddLabel} key={oddType}>
                    {ODD_TYPE_TITLES[oddType]}
                  </div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>

          <button className={styles.toggleButton} onClick={toggleOpen}>
            <ChevronUpIcon className={isOpen ? styles.iconOpened : styles.iconClosed} />
          </button>
        </div>
      </div>

      <AnimatePresence initial={false}>
        {isOpen && (
          <motion.div
            key="games"
            initial="collapsed"
            animate="open"
            exit="collapsed"
            variants={{
              open: { height: 'auto', opacity: 1 },
              collapsed: { height: 0, opacity: 0 },
            }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            style={{ overflow: 'hidden' }}
          >
            <div>
              <div className={styles.gamesContainer}>
                {games.map((game) => (
                  <GameRow
                    key={game.id}
                    game={game}
                    selectedOddGroup={selectedOddGroup}
                    isMobile={isMobile}
                    isTablet={isTablet}
                    supportsGroupedOdds={supportsGroupedOdds}
                  />
                ))}
              </div>

              <div className={styles.footer}>
                <button className={styles.loadMoreButton}>
                  <span>Load more</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
