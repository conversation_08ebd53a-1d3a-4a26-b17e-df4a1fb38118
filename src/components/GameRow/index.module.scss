@use '../../styles/media' as *;

.gameContainer {
  display: flex;
  flex-direction: column;
  height: 100px;
  background-color: var(--neutral-600);
  padding: 8px 8px 12px 16px;

  @include mobile {
    height: auto;
    padding: 12px 8px 12px 16px;
    border-bottom: 1px solid var(--neutral-500);
  }

  &:nth-child(1) {
    margin-top: 1px;

    @include mobile {
      margin-top: 0;
    }
  }

  &:last-child {
    margin-bottom: 1px;

    @include mobile {
      margin-bottom: 0;
    }
  }

  & .gameContent {
    display: flex;
    margin-top: 10px;
    justify-content: space-between;
    align-items: center;

    @include mobile {
      flex-direction: column;
      align-items: flex-start;
    }

    & .gameLeftContent {
      display: flex;
      row-gap: 8px;
      flex-direction: column;

      @include mobile {
        row-gap: 12px;
      }

      & .emptySpacer {
        width: 18px;

        @include mobile {
          display: none;
        }
      }

      & .teamRow {
        display: flex;
        align-items: center;

        & .teamName {
          font-weight: var(--font-weight-medium);
          font-size: var(--font-size-sm);
          line-height: 20px;
          text-align: center;
          color: var(--white);
          margin-left: 8px;
        }

        & .teamNameHighlighted {
          color: var(--orange);
        }
      }
    }


    & .scoreContainer {
      display: flex;
      margin-right: 8px;

      @include mobile {
        display: none;
      }
    }

    & .selectedOddType {
      display: none;

      @include mobile {
        display: block;
        color: var(--neutral-300);
        font-weight: var(--font-weight-bold);
        font-size: var(--font-size-xss);
        margin-top: 16px;
        margin-bottom: 4px;
      }
    }

    & .scoreColumn {
      display: flex;
      flex-direction: column;
      width: 24px;
      text-align: center;

      &:first-child {
        border-right: 1px solid var(--neutral-0);
      }
    }

    & .rightContent {
      display: flex;


      @include mobile {
        width: 100%;
      }
    }

    & .scoreText {
      font-size: var(--font-size-sm);
      line-height: 22px;
      color: var(--neutral-30)
    }

    & .oddsContainer {
      display: flex;
      align-items: center;
      column-gap: 4px;

      @include mobile {
        width: 100%;
      }

      & > button {
        height: 43px;

        @include laptop {
          width: 56px;
        }
      }

      & > button:nth-child(3) {
        margin-right: 16px;

        @include mobile {
          margin-right: 0;
        }
      }

      & > button {
        @include mobile {
          flex: 1;
        }
      }
    }

    & .moreButton {
      display: flex;
      align-items: center;
      column-gap: 9px;
      margin-left: 12px;
      width: 56px;
      height: 40px;
      background-color: transparent;
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-bold);
      color: var(--neutral-100);
      cursor: pointer;
      transition: 0.3s ease-in-out;

      @include mobile {
        width: 38px;
        height: 43px;
        background-color: var(--neutral-500);
        border-radius: 8px;
        justify-content: center;
        margin-left: 4px;
        flex-shrink: 0;
      }

      & svg {
        fill: var(--neutral-100);
        transition: 0.3s ease-in-out;
      }

      &:hover {
        color: var(--white);
      }

      &:hover svg {
        fill: var(--white);
      }
    }
  }

  .startIcon {
    margin-right: 12px;
    cursor: pointer;
    stroke: var(--neutral-100);
    transition: 0.2s ease-in-out;
    fill: transparent;

    &:hover {
      fill: var(--neutral-100);
    }
  }

  & .liveRound {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 11px;
    height: 11px;
    border-radius: 50%;
    border: 4px solid rgba(255, 73, 73, 0.2);

    & div {
      flex-shrink: 0;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background-color: var(--red)
    }
  }

  & .iconHidden {
    @include mobile {
      display: none;
    }
  }
}