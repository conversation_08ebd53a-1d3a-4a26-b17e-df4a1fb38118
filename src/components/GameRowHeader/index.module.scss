.mobileHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;

  & .mobileHeaderRight {
    display: flex;
    align-items: center;
    column-gap: 8px;
  }
}

.gameHeader {
  display: flex;
  column-gap: 8px;
  align-items: center;
}

.liveText {
  font-size: var(--font-size-xss);
  font-weight: var(--font-weight-semibold);
  color: var(--red);
  line-height: 20px;
}

.startDate {
  font-size: var(--font-size-xss);
  line-height: 20px;
  color: var(--neutral-300)
}

.startIcon {
  margin-right: 12px;
  cursor: pointer;
  stroke: var(--neutral-100);
  transition: 0.2s ease-in-out;
  fill: transparent;

  &:hover {
    fill: var(--neutral-100);
  }
}

.setText {
  background-color: var(--neutral-500);
  font-weight: var(--font-weight-semibold);
  color: var(--neutral-50);
  border-radius: 4px;
  padding: 1px 4px;
  font-size: var(--font-size-xss);
  line-height: 20px;
}

.liveRound {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 11px;
  height: 11px;
  border-radius: 50%;
  border: 4px solid rgba(255, 73, 73, 0.2);

  & div {
    flex-shrink: 0;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: var(--red)
  }
}

.startDateMobile {
  padding: 1px 4px;
  background-color: var(--neutral-500);
  border-radius: 4px;
  color: var(--neutral-50);
  font-size: var(--font-size-xss);
  font-weight: var(--font-weight-semibold);
}