import styles from './index.module.scss';
import dayjs from 'dayjs';
import StarOutlinedIcon from '../../assets/icons/star-outlined.svg?react';

type Props = {
  isMobile: boolean;
  isLive: boolean;
  startDate?: string;
  stage?: string;
};

export const GameRowHeader = ({ isLive, isMobile, stage, startDate }: Props) => {
  if (isMobile) {
    return (
      <div className={styles.mobileHeader}>
        <StarOutlinedIcon className={styles.startIcon} />

        {isLive ? (
          <div className={styles.mobileHeaderRight}>
            <div className={styles.liveRound}>
              <div />
            </div>

            <span className={styles.liveText}>Live</span>

            <span className={styles.setText}>{stage}</span>
          </div>
        ) : (
          <span className={styles.startDateMobile}>
            {dayjs(startDate).format('ddd, MMM D h:mm A')}
          </span>
        )}
      </div>
    );
  }

  return (
    <div className={styles.gameHeader}>
      {isLive ? (
        <>
          <div className={styles.liveRound}>
            <div />
          </div>

          <span className={styles.liveText}>Live</span>

          <span className={styles.setText}>{stage}</span>
        </>
      ) : (
        <span className={styles.startDate}>{dayjs(startDate).format('ddd, MMM D h:mm A')}</span>
      )}
    </div>
  );
};
