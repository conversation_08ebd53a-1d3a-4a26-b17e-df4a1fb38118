.customSelectWrapper {
  width: 40px;
  height: 40px;

  :global(.ant-select) {
    width: 40px;
  }

  :global(.ant-select-selector) {
      background-color: rgba(58, 63, 50, 1);
      padding: 8px;
      border-radius: 8px;
      border: none;
  }

  :global(.ant-select-dropdown.ant-select-item) {
    background-color: red
  }
}

:global(.customDropdown) {
  :global(.ant-select-item-option) {
    padding: 0;
  }

  :global(.rc-virtual-list-holder-inner) {
    gap: 4px;
  }

  :global(.ant-select-item-option-content) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}