import { useState } from 'react';
import { Select } from 'antd';
import { LANGUAGE_OPTIONS } from '../../utils/constants';
import styles from './index.module.scss';

export const LanguagePicker = () => {
  // TODO: Implement language change functionality
  const [selectedLanguage, setSelectedLanguage] = useState('en');

  return (
    <div className={styles.customSelectWrapper}>
      <Select
        value={selectedLanguage}
        onChange={setSelectedLanguage}
        options={LANGUAGE_OPTIONS.map((lang) => ({
          value: lang.value,
          label: (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <lang.flag style={{ width: 24, height: 24 }} />
            </div>
          ),
        }))}
        suffixIcon={null}
        className={styles.languageSelect}
        classNames={{
          popup: {
            root: 'customDropdown',
          },
        }}
      />
    </div>
  );
};
