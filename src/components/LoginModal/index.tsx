import { Modal } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useCallback, useState } from 'react';
import { type RootState, type AppDispatch } from '../../store';
import { closeModals } from '../../store/slices/modalSlice';
import { CloseOutlined } from '@ant-design/icons';
import styles from './index.module.scss';
import { LoginForm } from './LoginForm';
import { ForgotPasswordForm } from './ForgotPasswordForm';
import clsx from 'clsx';

export const LoginModal = () => {
  const [loginFormActive, setLoginFormActive] = useState(true);
  const dispatch = useDispatch<AppDispatch>();
  const { loginModalOpen } = useSelector((state: RootState) => state.modal);

  const handleCancel = useCallback(() => {
    dispatch(closeModals());
    setLoginFormActive(true);
  }, [dispatch]);

  return (
    <Modal
      open={loginModalOpen}
      onCancel={handleCancel}
      footer={null}
      centered
      transitionName=""
      maskTransitionName=""
      className={clsx(styles.login, 'login')}
      closeIcon={<CloseOutlined />}
    >
      {loginFormActive ? (
        <LoginForm onCancel={handleCancel} setLoginFormActive={setLoginFormActive} />
      ) : (
        <ForgotPasswordForm setLoginFormActive={setLoginFormActive} />
      )}
    </Modal>
  );
};
