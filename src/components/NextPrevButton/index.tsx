import type { FC } from 'react';
import styles from './index.module.scss';

import clsx from 'clsx';

import ArrowLeftIcon from '../../assets/icons/arrow-left.svg?react';
import ArrowRightIcon from '../../assets/icons/arrow-right.svg?react';

type Props = {
  prev?: boolean;
  disabled?: boolean;
  onClick?: () => void;
};

export const NextPrevButton: FC<Props> = ({ prev = true, disabled, onClick }) => {
  return (
    <button className={clsx(styles.button)} disabled={disabled} onClick={onClick}>
      {prev ? <ArrowLeftIcon /> : <ArrowRightIcon />}
    </button>
  );
};
