.passwordValidator {
  padding: 0;

  :global(input.ant-input) {
    box-shadow: none;
  }

  .validationRules {
    margin: 8px 0;
    padding: 12px 16px;
    background-color: var(--neutral-800);
    border-radius: 8px;
    border: 1px solid var(--neutral-600);

    .validationRule {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      font-size: 13px;
      transition: all 0.2s ease;

      &:last-child {
        margin-bottom: 0;
      }

      .icon {
        margin-right: 8px;
        font-size: var(--font-size-xs);
        width: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .label {
        flex: 1;
        font-weight: var(--font-weight-medium);
      }

      &.valid {
        color: var(--success);

        .icon {
          color: var(--success);
        }
      }

      &.invalid {
        color: var(--error);

        .icon {
          color: var(--error);
        }
      }
    }
  }
}
