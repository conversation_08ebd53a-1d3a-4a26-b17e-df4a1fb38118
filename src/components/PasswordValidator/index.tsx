import React, { useState } from 'react';
import { Form, Input } from 'antd';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import styles from './index.module.scss';
import clsx from 'clsx';

interface PasswordValidatorProps {
  name: string;
  placeholder?: string;
  label?: string;
  className?: string;
}

interface ValidationRule {
  key: string;
  label: string;
  test: (password: string) => boolean;
}

const validationRules: ValidationRule[] = [
  {
    key: 'length',
    label: 'Minimum 8 characters',
    test: (password: string) => password.length >= 8,
  },
  {
    key: 'lowercase',
    label: 'At least 1 lowercase letter',
    test: (password: string) => /[a-z]/.test(password),
  },
  {
    key: 'uppercase',
    label: 'At least 1 uppercase letter',
    test: (password: string) => /[A-Z]/.test(password),
  },
];

export const PasswordValidator: React.FC<PasswordValidatorProps> = ({
  name,
  placeholder = 'Enter your password',
  label,
  className = '',
}) => {
  const [password, setPassword] = useState('');
  const [showValidation, setShowValidation] = useState(false);

  const getValidationStatus = (rule: ValidationRule) => {
    return rule.test(password);
  };

  const areAllRulesValid = () => {
    return password.length > 0 && validationRules.every((rule) => rule.test(password));
  };

  const customValidator = (_: unknown, value: string) => {
    if (!value) {
      return Promise.reject(new Error('Password should be stated'));
    }

    const allRulesValid = validationRules.every((rule) => rule.test(value));

    if (!allRulesValid) {
      return Promise.reject(new Error('Please meet all password requirements'));
    }

    return Promise.resolve();
  };

  return (
    <div className={clsx(styles.passwordValidator, 'passwordValidator')}>
      <Form.Item
        name={name}
        rules={[{ validator: customValidator }]}
        label={label}
        className={clsx(className)}
      >
        <Input.Password
          placeholder={placeholder}
          onChange={(e) => {
            const value = e.target.value;
            setPassword(value);
            setShowValidation(value.length > 0);
          }}
        />
      </Form.Item>

      {showValidation && !areAllRulesValid() && (
        <div className={styles.validationRules}>
          {validationRules.map((rule) => {
            const isValid = getValidationStatus(rule);
            return (
              <div
                key={rule.key}
                className={`${styles.validationRule} ${isValid ? styles.valid : styles.invalid}`}
              >
                <span className={styles.icon}>
                  {isValid ? <CheckOutlined /> : <CloseOutlined />}
                </span>
                <span className={styles.label}>{rule.label}</span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
