import { Form, Input, Select, Row, Col } from 'antd';
import { validationRules } from '../../utils/validationRules';
import { COUNTRY_OPTIONS } from '../../utils/constants';
import { PasswordValidator } from '../PasswordValidator';

export const RegisterStep2 = () => (
  <>
    <Form.Item name="email" rules={validationRules.email}>
      <Input placeholder="Email" />
    </Form.Item>

    <Row gutter={8}>
      <Col span={8}>
        <Form.Item name="countryCode" initialValue="DE">
          <Select options={COUNTRY_OPTIONS} />
        </Form.Item>
      </Col>
      <Col span={16}>
        <Form.Item
          name="phone"
          rules={validationRules.phone}
          normalize={(value) => {
            if (value && !value.startsWith('+')) {
              return `+${value}`;
            }
            return value;
          }}
        >
          <Input placeholder="+49" />
        </Form.Item>
      </Col>
    </Row>

    <PasswordValidator name="password" placeholder="Enter your password" />
  </>
);
