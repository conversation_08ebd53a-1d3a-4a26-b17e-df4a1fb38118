import { Form, Checkbox } from 'antd';
import { validationRules } from '../../utils/validationRules';

export const RegisterStep3 = () => (
  <>
    <Form.Item name="privacyPolicy" valuePropName="checked" rules={validationRules.privacyPolicy}>
      <Checkbox>
        I am over 18 years of age and have accepted the{' '}
        <a href="#" onClick={(e) => e.preventDefault()}>
          Privacy Policy
        </a>
      </Checkbox>
    </Form.Item>

    <Form.Item
      name="termsConditions"
      valuePropName="checked"
      rules={validationRules.termsConditions}
    >
      <Checkbox>
        I have read and agree to the{' '}
        <a href="#" onClick={(e) => e.preventDefault()}>
          Terms and Conditions
        </a>
      </Checkbox>
    </Form.Item>
  </>
);
