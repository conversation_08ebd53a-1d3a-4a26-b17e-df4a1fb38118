import { useState, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { VerificationCodeInput } from '../VerificationCodeInput';
import { CountdownButton } from '../CountdownButton';
import KeyProtectedIcon from '../../assets/icons/key-protected.svg?react';
import { useVerifyEmailMutation, useResendVerificationCodeMutation } from '../../api/authApi';
import { setUser } from '../../store/slices/authSlice';
import type { RootState, AppDispatch } from '../../store';
import styles from './index.module.scss';
import { toast } from 'sonner';
import { AppButton } from '../AppButton';

export const RegisterStep4 = ({ onCancel }: { onCancel: () => void }) => {
  const dispatch = useDispatch<AppDispatch>();
  const [verificationCode, setVerificationCode] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const { user } = useSelector((state: RootState) => state.auth);

  const [verifyEmail, { isLoading: isVerifyingEmailLoading }] = useVerifyEmailMutation();
  const [resendCode, { isLoading: isResendingCodeLoading }] = useResendVerificationCodeMutation();

  const handleVerificationChange = useCallback(
    (value: string) => {
      // Clear error when user starts typing
      if (errorMessage) {
        setErrorMessage('');
      }
      setVerificationCode(value);
    },
    [errorMessage],
  );

  const handleVerify = async () => {
    if (verificationCode.length !== 6) {
      return;
    }

    try {
      const result = await verifyEmail({
        email: user?.email ?? '',
        code: verificationCode,
      }).unwrap();

      dispatch(setUser(result));

      toast.success(`Email verified successfully! Welcome! ${user?.name}`);
      onCancel();
    } catch (error: unknown) {
      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data?.message ||
        (error as { message?: string })?.message ||
        'Invalid verification code';

      setErrorMessage(errorMessage);
      setVerificationCode('');
      toast.error(errorMessage);
    }
  };

  const handleResendCode = async () => {
    setVerificationCode('');
    setErrorMessage('');

    try {
      await resendCode({ email: user?.email ?? '' }).unwrap();
      toast.success('Verification code sent to your email.');
    } catch (error: unknown) {
      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data?.message ||
        (error as { message?: string })?.message ||
        'Failed to resend verification code';

      toast.error(errorMessage);
    }
  };

  return (
    <div className={styles.verificationStep}>
      <KeyProtectedIcon />

      <div className={styles.title}>Enter the Code</div>

      <div className={styles.description}>
        Please enter the code we've sent to your email address{' '}
        <span className={styles.email}>{user?.email}</span>.
      </div>

      <div className={styles.verificationCode}>
        <VerificationCodeInput
          value={verificationCode}
          onChange={handleVerificationChange}
          error={!!errorMessage}
          disabled={isVerifyingEmailLoading}
          title="Code"
        />

        {errorMessage && <div className={styles.errorText}>{errorMessage}</div>}
      </div>

      <div className={styles.actions}>
        <CountdownButton
          initialTime={60}
          onResend={handleResendCode}
          loading={isResendingCodeLoading}
          disabled={isVerifyingEmailLoading}
        >
          Resend code
        </CountdownButton>

        <AppButton
          variant="primary"
          onClick={handleVerify}
          loading={isVerifyingEmailLoading}
          className={styles.primaryButton}
          disabled={
            verificationCode.length !== 6 || isVerifyingEmailLoading || isResendingCodeLoading
          }
          block
        >
          {isVerifyingEmailLoading ? 'Verifying...' : 'Verify'}
        </AppButton>
      </div>
    </div>
  );
};
