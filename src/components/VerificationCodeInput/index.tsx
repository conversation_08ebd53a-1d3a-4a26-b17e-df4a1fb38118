import { useEffect, useRef, useState } from 'react';
import { Input } from 'antd';
import type { InputRef } from 'antd';
import styles from './index.module.scss';

interface VerificationCodeInputProps {
  length?: number;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  error?: boolean;
  title?: string;
}

export const VerificationCodeInput = ({
  length = 6,
  value = '',
  onChange,
  disabled = false,
  error = false,
  title,
}: VerificationCodeInputProps) => {
  const [codes, setCodes] = useState<string[]>(Array(length).fill(''));
  const inputRefs = useRef<(InputRef | null)[]>([]);

  useEffect(() => {
    const newCodes = Array(length).fill('');
    for (let i = 0; i < Math.min(value.length, length); i++) {
      newCodes[i] = value[i] || '';
    }
    setCodes(newCodes);

    if (!value && inputRefs.current[0]) {
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 0);
    }
  }, [value, length]);

  useEffect(() => {
    onChange?.(codes.join(''));
  }, [codes, onChange]);

  const handleInputChange = (index: number, inputValue: string) => {
    // Only allow numbers
    const sanitizedValue = inputValue.replace(/[^0-9]/g, '');

    if (sanitizedValue.length <= 1) {
      const newCodes = [...codes];
      newCodes[index] = sanitizedValue;
      setCodes(newCodes);
    }
  };

  const handleInput = (index: number, e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = (e.target as HTMLInputElement).value;
    const sanitizedValue = inputValue.replace(/[^0-9]/g, '');

    // Auto-focus next input if any value is entered and we're not on the last input
    if (sanitizedValue && index < length - 1) {
      setTimeout(() => {
        inputRefs.current[index + 1]?.focus();
      }, 0);
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace') {
      if (!codes[index] && index > 0) {
        // If current input is empty and backspace is pressed, focus previous input
        inputRefs.current[index - 1]?.focus();
      } else {
        // Clear current input
        const newCodes = [...codes];
        newCodes[index] = '';
        setCodes(newCodes);
      }
    } else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowRight' && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData('text');
    const sanitizedData = pasteData.replace(/[^0-9]/g, '');

    const newCodes = [...codes];
    for (let i = 0; i < Math.min(sanitizedData.length, length); i++) {
      newCodes[i] = sanitizedData[i];
    }
    setCodes(newCodes);

    // Focus the next empty input or the last input
    const nextEmptyIndex = newCodes.findIndex((code) => !code);
    const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : length - 1;
    inputRefs.current[focusIndex]?.focus();
  };

  const handleFocus = (index: number) => {
    inputRefs.current[index]?.select();
  };

  return (
    <div className={styles.outerContainer}>
      {title && <p>{title}</p>}
      <div className={styles.container}>
        {codes.map((code, index) => (
          <Input
            key={index}
            ref={(el) => (inputRefs.current[index] = el)}
            value={code}
            onChange={(e) => handleInputChange(index, e.target.value)}
            onInput={(e) => handleInput(index, e)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            onPaste={handlePaste}
            onFocus={() => handleFocus(index)}
            disabled={disabled}
            className={`${styles.input} ${error ? styles.error : ''}`}
            maxLength={1}
            autoComplete="off"
          />
        ))}
      </div>
    </div>
  );
};
