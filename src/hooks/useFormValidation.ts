import { useCallback, useState, useEffect } from 'react';
import { type FormInstance } from 'antd';

interface UseFormValidationParams {
  form?: FormInstance;
  currentStep?: number;
  stepFieldsMap?: Record<number, readonly string[]>;
  fields?: readonly string[];
  enabled?: boolean;
}

export const useFormValidation = ({
  form,
  currentStep = 1,
  stepFieldsMap,
  fields,
  enabled = true,
}: UseFormValidationParams) => {
  const [isStepValid, setIsStepValid] = useState(false);

  const checkCurrentStepValidity = useCallback(() => {
    if (!enabled || !form || !form.getFieldsError) {
      setIsStepValid(false);
      return;
    }

    try {
      if (!form.getFieldsValue || !form.isFieldsTouched) {
        setIsStepValid(false);
        return;
      }
    } catch {
      setIsStepValid(false);
      return;
    }

    let fieldsForCurrentStep: string[] = [];

    if (fields) {
      fieldsForCurrentStep = [...fields];
    } else if (stepFieldsMap) {
      fieldsForCurrentStep = [...(stepFieldsMap[currentStep] || [])];
    }

    if (fieldsForCurrentStep.length === 0) {
      setIsStepValid(true);
      return;
    }

    try {
      const fieldErrors = form.getFieldsError(fieldsForCurrentStep);
      const hasErrors = fieldErrors.some(({ errors }) => errors.length > 0);

      const fieldValues = form.getFieldsValue(fieldsForCurrentStep);
      const hasAllRequiredValues = fieldsForCurrentStep.every((fieldName) => {
        const value = fieldValues[fieldName];

        if (typeof value === 'boolean') {
          return value === true;
        }

        return value !== undefined && value !== null && value !== '';
      });

      const fieldsTouched = form.isFieldsTouched(fieldsForCurrentStep, false);

      const isValid = !hasErrors && hasAllRequiredValues && fieldsTouched;
      setIsStepValid(isValid);
    } catch {
      setIsStepValid(false);
    }
  }, [form, currentStep, stepFieldsMap, fields, enabled]);

  useEffect(() => {
    checkCurrentStepValidity();
  }, [checkCurrentStepValidity]);

  return {
    isStepValid,
    checkCurrentStepValidity,
    setIsStepValid,
  };
};
