import { useMemo } from 'react';
import { GROUPED_ODDS_SPORTS } from '../utils/constants.ts';
import type { SportType } from '../types/entities.ts';

interface UseGroupedOddsParams {
  activeSportType: SportType;
  isTablet: boolean;
  isMobile: boolean;
}

export const useGroupedOdds = ({ activeSportType, isTablet, isMobile }: UseGroupedOddsParams) => {
  const supportedGroups = GROUPED_ODDS_SPORTS[activeSportType];

  const supportsGroupedOdds = Boolean(
    supportedGroups?.tablet?.length || supportedGroups?.mobile?.length,
  );

  const dropdownOptions = useMemo(() => {
    if (isTablet) {
      return (
        supportedGroups?.tablet.map((item) => ({
          label: item,
          value: item,
        })) || []
      );
    }
    if (isMobile) {
      return (
        supportedGroups?.mobile.map((item) => ({
          label: item,
          value: item,
        })) || []
      );
    }
    return [];
  }, [isMobile, isTablet, supportedGroups]);

  return {
    supportedGroups,
    supportsGroupedOdds,
    dropdownOptions,
  };
};
