import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { type AppDispatch } from '../store';
import { openResetPasswordModal } from '../store/slices/modalSlice';

export const useResetPasswordToken = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const resetToken = searchParams.get('token');
    const action = searchParams.get('action');

    if (resetToken && action === 'reset-password') {
      dispatch(openResetPasswordModal());
    }
  }, [searchParams, dispatch]);
};
