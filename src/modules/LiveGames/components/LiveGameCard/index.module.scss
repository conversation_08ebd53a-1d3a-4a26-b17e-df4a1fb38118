.card {
  display: flex;
  align-items: center;
  width: auto;
  min-width: 70px;
  height: 100%;
  border-radius: 8px;
  background: var(--neutral-500);
  padding: 0 8px;
  column-gap: 4px;
  cursor: pointer;
  transition: 0.3s ease-in-out;
  border: 1.5px solid var(--neutral-500);

  &:hover {
    border-color: var(--neutral-400);
  }

  &:hover .image {
    opacity: 1;
  }

  &:hover .counter {
    background: var(--neutral-400);
    color: var(--white);
  }

  .image {
    width: 32px;
    height: 32px;
    opacity: 50%;
    transition: 0.3s ease-in-out;
  }

  .imageActive {
    opacity: 1;
  }

  .counter {
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--neutral-300);
    font-size: var(--font-size-xs);
    transition: 0.3s ease-in-out;
    border-radius: 50%;
  }

  .counterActive {
    background: var(--primary-500);
    color: var(--neutral-700);
  }

  .label {
    white-space: nowrap;
    overflow: hidden;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    color: var(--white2);
    margin-right: 4px;
  }
}

.cardActive {
  border-color: var(--primary-500);
  background: linear-gradient(90deg, var(--primary-750) 0%, var(--primary-750-transparent) 100%);

  &:hover {
    border-color: var(--primary-500);
    background: linear-gradient(90deg, var(--primary-750) 0%, var(--primary-750-transparent) 100%);
  }

  &:hover .counter {
    background: var(--primary-500);
    color: var(--neutral-700);
  }
}
