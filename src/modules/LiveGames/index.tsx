import styles from './index.module.scss';
import { NextPrevButton } from '../../components/NextPrevButton';
import { useLayoutEffect, useRef, useState } from 'react';
import type SwiperClass from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import { COEF_DIRECTION, type GameLeague, ODD_TYPE, SportType } from '../../types/entities.ts';
import { LiveGameCard } from './components/LiveGameCard';
import clsx from 'clsx';

import LiveGamesIcon from '../../assets/icons/live-games.svg?react';
import { GameLeagueSection } from '../../components/GameLeagueSection';

import teamOneMini from '../../assets/mocks/team-1-mini.png';
import teamTwoMini from '../../assets/mocks/team-2-mini.png';
import teamThreeMini from '../../assets/mocks/team-3-mini.png';
import teamFourMini from '../../assets/mocks/team-4-mini.png';
import teamFiveMini from '../../assets/mocks/team-5-mini.png';
import teamSixMini from '../../assets/mocks/team-6-mini.png';
import { useResponsive } from '../../hooks';
import { AppDropdown } from '../../components/AppDropdown';
import { GAMES_SORTING_ORDER_OPTIONS } from '../../utils/constants.ts';
import { useDispatch, useSelector } from 'react-redux';
import type { RootState } from '../../store';
import { setActiveSportType } from '../../store/slices/sports.ts';
import { useGroupedOdds } from '../../hooks/useGroupedOdds.ts';

export type LiveGameT = {
  id: number;
  sportType: SportType;
  liveGamesCount: number;
};

// eslint-disable-next-line react-refresh/only-export-components
export const mockLiveLeagues: GameLeague = {
  id: 'league-uefa-1',
  leagueName: 'UEFA Champions League',
  gameCount: 3,
  games: [
    {
      id: 'game-1',
      isLive: true,
      tournament: 'UEFA Champions League',
      stage: '1st set',
      startDate: new Date().toISOString(),
      teamOne: {
        name: 'Juventus',
        logo: teamOneMini,
        score: 1,
      },
      teamTwo: {
        name: 'Valley Knights',
        logo: teamTwoMini,
        score: 2,
      },
      odds: [
        {
          value: 0.62,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.ONE,
          id: 1,
        },
        {
          value: 0.85,
          direction: COEF_DIRECTION.none,
          isLocked: false,
          oddType: ODD_TYPE.DRAW,
          id: 2,
        },
        { value: 0, direction: COEF_DIRECTION.none, isLocked: true, oddType: ODD_TYPE.TWO, id: 3 },
        {
          value: 2.03,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.OVER,
          id: 4,
        },
        {
          value: 0.35,
          direction: COEF_DIRECTION.down,
          isLocked: false,
          oddType: ODD_TYPE.TOTAL,
          id: 5,
        },
        {
          value: 0,
          direction: COEF_DIRECTION.none,
          isLocked: true,
          oddType: ODD_TYPE.UNDER,
          id: 6,
        },
      ],
      hasMoreMarkets: true,
    },
    {
      id: 'game-2',
      isLive: true,
      tournament: 'UEFA Champions League',
      stage: '1st set',
      startDate: new Date().toISOString(),
      teamOne: {
        name: 'Paris Saint-Germain',
        logo: teamThreeMini,
        score: 5,
      },
      teamTwo: {
        name: 'Cypress Eagles',
        logo: teamFourMini,
        score: 0,
      },
      odds: [
        {
          value: 0.25,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.ONE,
          id: 1,
        },
        {
          value: 0.7,
          direction: COEF_DIRECTION.down,
          isLocked: false,
          oddType: ODD_TYPE.DRAW,
          id: 2,
        },
        {
          value: 0.21,
          direction: COEF_DIRECTION.down,
          isLocked: false,
          oddType: ODD_TYPE.TWO,
          id: 3,
        },
        {
          value: 1.49,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.OVER,
          id: 4,
        },
        {
          value: 0.89,
          direction: COEF_DIRECTION.none,
          isLocked: false,
          oddType: ODD_TYPE.TOTAL,
          id: 5,
        },
        {
          value: 0,
          direction: COEF_DIRECTION.none,
          isLocked: true,
          oddType: ODD_TYPE.UNDER,
          id: 6,
        },
      ],
      hasMoreMarkets: true,
    },
    {
      id: 'game-3',
      isLive: true,
      tournament: 'UEFA Champions League',
      stage: '1st set',
      startDate: new Date().toISOString(),
      teamOne: {
        name: 'Inter Milan',
        logo: teamFiveMini,
        score: 0,
      },
      teamTwo: {
        name: 'Horizon Titans',
        logo: teamSixMini,
        score: 4,
      },
      odds: [
        {
          value: 0.57,
          direction: COEF_DIRECTION.none,
          isLocked: false,
          oddType: ODD_TYPE.ONE,
          id: 1,
        },
        {
          value: 0.34,
          direction: COEF_DIRECTION.down,
          isLocked: false,
          oddType: ODD_TYPE.DRAW,
          id: 2,
        },
        {
          value: 0.12,
          direction: COEF_DIRECTION.down,
          isLocked: false,
          oddType: ODD_TYPE.TWO,
          id: 3,
        },
        {
          value: 1.76,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.OVER,
          id: 4,
        },
        {
          value: 0.67,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.TOTAL,
          id: 5,
        },
        {
          value: 0,
          direction: COEF_DIRECTION.none,
          isLocked: true,
          oddType: ODD_TYPE.UNDER,
          id: 6,
        },
      ],
      hasMoreMarkets: true,
    },
  ],
};

const liveGamesMock: LiveGameT[] = Object.values(SportType).map((sportType, index) => ({
  id: index,
  sportType: sportType as SportType,
  liveGamesCount: Math.round(Math.random() * 30),
}));

export const LiveGames = () => {
  const [selectedOddGroup, setSelectedOddGroup] = useState('1x2');

  const [selectedOrder, setSelectedOrder] = useState('Date');

  const { isMobile, isTablet } = useResponsive();
  const [swiperInstance, setSwiperInstance] = useState<SwiperClass | null>(null);
  const refs = useRef<Record<string, HTMLButtonElement | null>>({});
  const [widths, setWidths] = useState<Record<string, number>>({});

  const dispatch = useDispatch();

  const activeSportType = useSelector((state: RootState) => state.sports.activeSportType);

  const { supportsGroupedOdds, dropdownOptions, supportedGroups } = useGroupedOdds({
    activeSportType,
    isMobile,
    isTablet,
  });

  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  const handleSlideChange = (swiper: SwiperClass) => {
    setIsBeginning(swiper.isBeginning);
    setIsEnd(swiper.isEnd);
  };

  const swipeNext = () => {
    swiperInstance?.slideNext();
  };

  const swipePrev = () => {
    swiperInstance?.slidePrev();
  };

  const handleChangeActiveSortType = (newSportType: SportType) => {
    dispatch(setActiveSportType(newSportType));
  };

  useLayoutEffect(() => {
    const newWidths: Record<string, number> = {};

    liveGamesMock.forEach((event) => {
      const el = refs.current[event.id];
      if (el) {
        const width = el.offsetWidth;
        newWidths[event.id] = Math.max(width, 70);
      }
    });

    setWidths(newWidths);
    setTimeout(() => swiperInstance?.update(), 0);
  }, [activeSportType, swiperInstance]);

  return (
    <div className={styles.wrapper}>
      <div className={styles.topHeader}>
        <div className={styles.headerTopLeft}>
          <LiveGamesIcon className={styles.liveGamesIcon} />
          <h2 className={styles.title}>Live Games</h2>
        </div>

        {supportedGroups && isTablet && (
          <AppDropdown
            options={dropdownOptions}
            selected={selectedOddGroup}
            onChange={(value) => setSelectedOddGroup(value)}
          />
        )}
      </div>

      {isMobile && (
        <div className={styles.headerMiddleMobile}>
          {supportedGroups && (
            <AppDropdown
              options={dropdownOptions}
              selected={selectedOddGroup}
              onChange={(value) => setSelectedOddGroup(value)}
            />
          )}

          <AppDropdown
            label="Order by"
            options={GAMES_SORTING_ORDER_OPTIONS}
            selected={selectedOrder}
            onChange={(value) => setSelectedOrder(value)}
          />
        </div>
      )}

      <div className={styles.header}>
        <div className={styles.swiperContainer}>
          <Swiper
            onSwiper={setSwiperInstance}
            onSlideChange={handleSlideChange}
            slidesPerView={'auto'}
            spaceBetween={20}
          >
            {liveGamesMock.map((event) => (
              <SwiperSlide
                key={event.id}
                style={{
                  width: widths[event.id] ?? 70,
                }}
                className={clsx(styles.card)}
              >
                <LiveGameCard
                  {...event}
                  refs={refs}
                  onClickCard={handleChangeActiveSortType}
                  isActive={activeSportType === event.sportType}
                />
              </SwiperSlide>
            ))}
            <SwiperSlide aria-hidden={true} />
          </Swiper>
        </div>

        <div className={styles.navButtons}>
          <div className={styles.prev}>
            <NextPrevButton prev disabled={isBeginning} onClick={swipePrev} />
          </div>
          <div className={styles.next}>
            <NextPrevButton prev={false} disabled={isEnd} onClick={swipeNext} />
          </div>
        </div>
      </div>

      <div>
        <GameLeagueSection
          {...mockLiveLeagues}
          sportType={activeSportType}
          isMobile={isMobile}
          isTablet={isTablet}
          selectedOddGroup={selectedOddGroup}
          supportsGroupedOdds={supportsGroupedOdds}
        />
      </div>
    </div>
  );
};
