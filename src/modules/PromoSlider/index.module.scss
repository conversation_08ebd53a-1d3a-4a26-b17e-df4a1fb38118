@use '../../styles/media' as *;

.sliderWrapper {
  position: relative;

  .swiperContainer {
    overflow-x: hidden;
    width: 100%;
  }

  .slide {
    position: relative;
    height: 300px;
    border-radius: 16px;
    overflow: hidden;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;

    display: flex;
    align-items: flex-end;
    padding: 24px;

    & .slideContent {
      position: relative;
      z-index: 1;
      background: var(--black-overlay);
      padding: 16px;
      border-radius: 8px;
      max-width: 80%;
      color: var(--white);
    }
  }
}

.controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 50px;
  margin-top: 16px;

  @include tablet {
    gap: 16px;
  }

  @include mobile {
    gap: 16px;
  }
}

.arrowButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  cursor: pointer;
  background: transparent;
  flex-shrink: 0;

  @include tablet {
    background-color: var(--neutral-500);
    border-radius: 6px;
  }

  @include mobile {
    background-color: var(--neutral-500);
    border-radius: 6px;
  }
}

.bullets {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.bullet {
  width: 35px;
  height: 3px;
  background: var(--neutral-200-30op);
  border-radius: 8px;
  cursor: pointer;
  transition: 0.3s;

  &.active {
    background: var(--primary-500);
    width: 73px;
  }
}
