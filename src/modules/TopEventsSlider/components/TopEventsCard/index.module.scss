.card {
  background: var(--neutral-600);
  padding: 12px;
  display: flex;
  flex-direction: column;
  color: var(--white);
  height: 100%;
}

.leagueRow {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--gray-light);
  gap: 16px;
  margin-bottom: 23px;

  .leagueHeader {
    display: flex;
    column-gap: 8px;
    align-items: flex-start;

    flex: 1;
    min-width: 0;
  }

  .sportIcon {
    width: 22px;
    height: 22px;
  }

  .league {
    display: block;
    align-items: center;
    font-size: var(--font-size-sm);
    line-height: 20px;
    color: var(--neutral-300);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .date {
    display: flex;
    width: max-content;
    flex-shrink: 0;
    height: 18px;
    padding: 1px 4px;
    background-color: var(--neutral-500);
    color: var(--neutral-50);
    font-size: var(--font-size-xss);
    font-weight: var(--font-weight-semibold);
    border-radius: 4px;
  }
}

.teamsRow {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 16px;
}

.team {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--white2);
}

.logo {
  width: 60px;
  height: 60px;
  margin-bottom: 12px;
}

.oddsRow {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  gap: 4px;

  .button {
    display: flex;
    align-items: center;
    width: 38px;
    height: 38px;
    background: var(--neutral-500);
    border-radius: 8px;
    justify-content: center;
    cursor: pointer;
    transition: 0.3s ease;

    & svg {
      fill: var(--neutral-200);
    }

    &:hover {
      background: var(--primary-800);
    }

    &:hover svg {
      fill: var(--primary-500);
    }
  }
}
