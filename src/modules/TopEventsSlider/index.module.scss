@use "../../styles/media" as *;

.wrapper {
  margin-top: 54px;
}

.swiperContainer {
  overflow-x: hidden;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .headerLeft {
    display: flex;
    column-gap: 12px;
    align-items: center;
  }

  .title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--white2);
  }

  .navButtons {
    display: flex;
    gap: 8px;
  }
}

.card {
  width: 340px;
  height: 230px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;

  @include mobile {
    width: 100%;
    max-width: 100%;
    height: 252px;
  }
}
