import { NextPrevButton } from '../../components/NextPrevButton';
import { useState } from 'react';
import type SwiperClass from 'swiper';
import { TopEventCard, type TopEventCardProps } from './components/TopEventsCard';
import { Swiper, SwiperSlide } from 'swiper/react';
import styles from './index.module.scss';

import TopEventsIcon from '../../assets/icons/top-events.svg?react';

import TeamOne from '../../assets/mocks/team-1.png';
import TeamTwo from '../../assets/mocks/team2.png';
import TeamThree from '../../assets/mocks/team3.png';
import TeamFour from '../../assets/mocks/team4.png';
import TeamFive from '../../assets/mocks/team5.png';
import TeamSix from '../../assets/mocks/team6.png';
import { COEF_DIRECTION, SportType } from '../../types/entities.ts';
import { useResponsive } from '../../hooks';

const events: TopEventCardProps[] = [
  {
    id: 1,
    date: 'Jul 16, 8:45 PM',
    league: 'International, Euro Cup',
    teams: ['Young Boys Bern', 'Manchester United'],
    logos: [TeamOne, TeamTwo],
    odds: [
      {
        id: 1,
        value: 1.3,
        direction: COEF_DIRECTION.up,
        isLocked: false,
      },
      {
        id: 2,
        value: 2.5,
        direction: COEF_DIRECTION.down,
        isLocked: false,
      },
      {
        id: 3,
        value: 5,
        direction: COEF_DIRECTION.none,
        isLocked: true,
      },
    ],
    activeOddId: 2,
    sportType: SportType.FOOTBALL,
  },
  {
    id: 2,
    date: 'Jul 16, 8:45 PM',
    league: 'International, Universiade, Women',
    teams: ['Finland', 'Czechia'],
    logos: [TeamThree, TeamFour],
    odds: [
      {
        id: 1,
        value: 1.3,
        direction: COEF_DIRECTION.up,
        isLocked: false,
      },
      {
        id: 2,
        value: 2.5,
        direction: COEF_DIRECTION.down,
        isLocked: false,
      },
      {
        id: 3,
        value: 5,
        direction: COEF_DIRECTION.none,
        isLocked: true,
      },
    ],
    activeOddId: 2,
    sportType: SportType.TENNIS,
  },
  {
    id: 3,
    date: 'Jul 16, 8:45 PM',
    league: 'International, Euro Cup',
    teams: ['FC Bayern Munich', 'Opponent'],
    logos: [TeamFive, TeamSix],
    odds: [
      { id: 1, value: 1.3, direction: COEF_DIRECTION.up, isLocked: false },
      {
        id: 2,
        value: 2.5,
        direction: COEF_DIRECTION.down,
        isLocked: false,
      },
      {
        id: 3,
        value: 5,
        direction: COEF_DIRECTION.none,
        isLocked: true,
      },
    ],
    activeOddId: null,
    sportType: SportType.BASKETBALL,
  },
  {
    id: 4,
    date: 'Jul 16, 8:45 PM',
    league: 'International, Euro Cup',
    teams: ['FC Bayern Munich', 'Opponent'],
    logos: [TeamFive, TeamSix],
    odds: [
      {
        id: 1,
        value: 1.3,
        direction: COEF_DIRECTION.up,
        isLocked: false,
      },
      {
        id: 2,
        value: 2.5,
        direction: COEF_DIRECTION.down,
        isLocked: false,
      },
      {
        id: 3,
        value: 5,
        direction: COEF_DIRECTION.none,
        isLocked: true,
      },
    ],
    activeOddId: null,
    sportType: SportType.ICE_HOCKEY,
  },
  {
    id: 5,
    date: 'Jul 16, 8:45 PM',
    league: 'International, Euro Cup',
    teams: ['FC Bayern Munich', 'Opponent'],
    logos: [TeamFive, TeamSix],
    odds: [
      {
        id: 1,
        value: 1.3,
        direction: COEF_DIRECTION.up,
        isLocked: false,
      },
      {
        id: 2,
        value: 2.5,
        direction: COEF_DIRECTION.down,
        isLocked: false,
      },
      {
        id: 3,
        value: 5,
        direction: COEF_DIRECTION.none,
        isLocked: true,
      },
    ],
    activeOddId: null,
    sportType: SportType.ICE_HOCKEY,
  },
  {
    id: 6,
    date: 'Jul 16, 8:45 PM',
    league: 'International, Euro Cup',
    teams: ['FC Bayern Munich', 'Opponent'],
    logos: [TeamFive, TeamSix],
    odds: [
      {
        id: 1,
        value: 1.3,
        direction: COEF_DIRECTION.up,
        isLocked: false,
      },
      {
        id: 2,
        value: 2.5,
        direction: COEF_DIRECTION.down,
        isLocked: false,
      },
      {
        id: 3,
        value: 5,
        direction: COEF_DIRECTION.none,
        isLocked: true,
      },
    ],
    activeOddId: null,
    sportType: SportType.ICE_HOCKEY,
  },
];

export const TopEventsSlider = () => {
  const [swiperInstance, setSwiperInstance] = useState<SwiperClass | null>(null);

  const { isMobile } = useResponsive();

  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  const handleSlideChange = (swiper: SwiperClass) => {
    setIsBeginning(swiper.isBeginning);
    setIsEnd(swiper.isEnd);
  };

  const swipeNext = () => {
    swiperInstance?.slideNext();
  };

  const swipePrev = () => {
    swiperInstance?.slidePrev();
  };

  return (
    <div className={styles.wrapper}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <TopEventsIcon />
          <h2 className={styles.title}>Top Events</h2>
        </div>

        <div className={styles.navButtons}>
          <div className={styles.prev}>
            <NextPrevButton prev disabled={isBeginning} onClick={swipePrev} />
          </div>
          <div className={styles.next}>
            <NextPrevButton prev={false} disabled={isEnd} onClick={swipeNext} />
          </div>
        </div>
      </div>
      <div className={styles.swiperContainer}>
        <Swiper
          key={isMobile ? 'mobile' : 'desktop'}
          onSwiper={setSwiperInstance}
          onSlideChange={handleSlideChange}
          slidesPerView={isMobile ? 1 : 'auto'}
          spaceBetween={isMobile ? 12 : 16}
        >
          {events.map((event) => (
            <SwiperSlide key={event.id} className={styles.card}>
              <TopEventCard {...event} />
            </SwiperSlide>
          ))}
          <SwiperSlide aria-hidden={true} style={{ width: 40 }} />
        </Swiper>
      </div>
    </div>
  );
};
