@use "../../styles/media" as *;

.wrapper {
  margin-top: 62px;
}

.swiperContainer {
  overflow-x: hidden;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .headerLeft {
    display: flex;
    column-gap: 12px;
    align-items: center;
  }

  .title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--white2);
  }

  .navButtons {
    display: flex;
    gap: 8px;
  }
}

.swiper {
  :global(.swiper-wrapper) {
    column-gap: 16px;

    @include mobile {
      column-gap: 12px;
    }
  }
}

.card {
  width: 180px;
  height: 228px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  border: 2px solid var(--black2);
  background: var(--neutral-dark-bg);
  cursor: pointer;

  @include mobile {
    width: 126px;
    height: 160px;
  }


  .image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .overlay {
    position: absolute;
    inset: 0;
    pointer-events: none;
    z-index: 1;

    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 112px;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, var(--black) 100%);
      z-index: 3;

      @include mobile {
        height: 66px;
      }
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 90px;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0.01) 0%, var(--primary-500) 100%);
      z-index: 4;

      @include mobile {
        height: 66px;
      }
    }


    .labelBg {
      position: absolute;
      bottom: 17px;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 15;

      @include mobile {
        height: 30px;
      }
    }

    .title {
      color: var(--white);
      font-size: var(--font-size-xxl);
      font-weight: var(--font-weight-bold);
      font-family: 'Poppins', sans-serif;
      line-height: 32px;
      text-transform: uppercase;
      text-align: center;
      letter-spacing: -0.6px;
      position: relative;
      z-index: 16;
      padding: 2px 4px;
      text-shadow: 0 4px 4px var(--black-text-shadow);

      @include mobile {
        font-size: var(--font-size-lg);
        line-height: 20px;
      }
    }
  }
}
