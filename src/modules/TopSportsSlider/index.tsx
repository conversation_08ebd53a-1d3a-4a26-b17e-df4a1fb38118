import { Swiper, SwiperSlide } from 'swiper/react';

import 'swiper/css';
import styles from './index.module.scss';

import FootballImage from '../../assets/mocks/soccer.png';
import AmericanFootballImage from '../../assets/mocks/american-football.png';
import Basketball from '../../assets/mocks/basketball.png';
import Baseball from '../../assets/mocks/baseball.png';
import Hockey from '../../assets/mocks/hockey.png';
import Tennis from '../../assets/mocks/tennis.png';

import TopSportsIcon from '../../assets/icons/top-sports.svg?react';

import { NextPrevButton } from '../../components/NextPrevButton';
import { useState } from 'react';

import type SwiperClass from 'swiper';
import { useResponsive } from '../../hooks';

const sports = [
  { title: 'SOCCER', image: FootballImage },
  { title: 'AMERICAN FOOTBALL', image: AmericanFootballImage },
  { title: 'BASKETBALL', image: Basketball },
  { title: 'BASEBALL', image: Baseball },
  { title: 'HOCKEY', image: Hockey },
  { title: 'TENNIS1', image: Tennis },
  { title: 'TENNIS2', image: Tennis },
  { title: 'TENNIS3', image: Tennis },
];

export const TopSportsSlider = () => {
  const [swiperInstance, setSwiperInstance] = useState<SwiperClass | null>(null);

  const { isMobile } = useResponsive();

  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  const handleSlideChange = (swiper: SwiperClass) => {
    setIsBeginning(swiper.isBeginning);
    setIsEnd(swiper.isEnd);
  };

  const swipeNext = () => {
    swiperInstance?.slideNext();
  };

  const swipePrev = () => {
    swiperInstance?.slidePrev();
  };

  return (
    <div className={styles.wrapper}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <TopSportsIcon />
          <h2 className={styles.title}>Top Sports</h2>
        </div>

        <div className={styles.navButtons}>
          <div className={styles.prev}>
            <NextPrevButton prev disabled={isBeginning} onClick={swipePrev} />
          </div>
          <div className={styles.next}>
            <NextPrevButton prev={false} disabled={isEnd} onClick={swipeNext} />
          </div>
        </div>
      </div>

      <div className={styles.swiperContainer}>
        <Swiper
          onSwiper={(swiper) => {
            setSwiperInstance(swiper);
          }}
          slidesPerView="auto"
          key={isMobile ? 'mobile' : 'desktop'}
          spaceBetween={isMobile ? 12 : 16}
          onSlideChange={handleSlideChange}
        >
          {sports.map((sport) => (
            <SwiperSlide key={sport.title} className={styles.card}>
              <img src={sport.image} alt={sport.title} className={styles.image} />
              <div className={styles.overlay}>
                <div className={styles.labelBg}>
                  <span className={styles.title}>{sport.title}</span>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};
