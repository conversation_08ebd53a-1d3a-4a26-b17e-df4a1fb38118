@use '../../styles/media' as *;

.profilePage {
  min-height: 100vh;
  background-color: var(--neutral-dark-bg);
  padding-top: 40px;
  padding-bottom: 40px;

  @include mobile {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;

  @include mobile {
    padding: 0 15px;
  }
}

.title {
  color: var(--white);
  font-size: var(--font-size-heading);
  font-weight: var(--font-weight-bold);
  margin: 0 0 32px 0;

  @include mobile {
    font-size: var(--font-size-xxl);
    margin-bottom: 24px;
  }
}

.content {
  background-color: var(--neutral-800);
  border-radius: 12px;
  padding: 40px;

  @include mobile {
    padding: 24px 15px;
  }

  p {
    color: var(--neutral-300);
    font-size: var(--font-size-md);
  }
}
