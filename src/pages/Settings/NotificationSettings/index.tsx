import { Switch, Skeleton } from 'antd';
import { useEffect, useState, useTransition } from 'react';
import { toast } from 'sonner';
import {
  useGetNotificationSettingsQuery,
  useUpdateNotificationSettingsMutation,
  type NotificationSettings as NotificationSettingsType,
} from '../../../api/userApi';
import { NOTIFICATION_OPTIONS } from '../../../utils/constants';
import styles from './NotificationSettings.module.scss';
import clsx from 'clsx';

const NotificationSettings = () => {
  const {
    data: serverSettings,
    isLoading: isLoadingSettings,
    error: loadError,
  } = useGetNotificationSettingsQuery();

  const [updateSettings] = useUpdateNotificationSettingsMutation();
  const [, startTransition] = useTransition();

  const [localSettings, setLocalSettings] = useState<NotificationSettingsType>({
    emailMarketing: false,
    bonusNotifications: true,
    winNotifications: false,
    depositNotifications: false,
    withdrawalNotifications: false,
  });

  const [pendingUpdates, setPendingUpdates] = useState<Set<keyof NotificationSettingsType>>(
    new Set(),
  );

  useEffect(() => {
    if (serverSettings) {
      setLocalSettings(serverSettings);
    }
  }, [serverSettings]);

  useEffect(() => {
    if (loadError) {
      const errorMessage =
        (loadError as { data?: { message?: string }; message?: string })?.data?.message ||
        (loadError as { message?: string })?.message ||
        'Failed to load notification settings';
      toast.error(errorMessage);
    }
  }, [loadError]);

  const handleSettingChange = (key: keyof NotificationSettingsType, value: boolean) => {
    setPendingUpdates((prev) => new Set(prev).add(key));

    const updatedSettings = {
      ...localSettings,
      [key]: value,
    };

    startTransition(() => {
      setLocalSettings(updatedSettings);
    });

    const performUpdate = async () => {
      try {
        // await updateSettings(updatedSettings).unwrap();
        toast.success('Notification setting updated successfully');
      } catch (error: unknown) {
        setLocalSettings((prev) => ({
          ...prev,
          [key]: !value,
        }));

        const errorMessage =
          (error as { data?: { message?: string }; message?: string })?.data?.message ||
          (error as { message?: string })?.message ||
          'Failed to update notification setting';
        toast.error(errorMessage);
        console.error('Update notification setting error:', error);
      } finally {
        setPendingUpdates((prev) => {
          const newSet = new Set(prev);
          newSet.delete(key);
          return newSet;
        });
      }
    };

    performUpdate();
  };

  if (isLoadingSettings) {
    return (
      <div className={clsx(styles.notificationSettings, styles.skeleton)}>
        <div className={styles.innerWrapper}>
          <div className={styles.header}>
            <Skeleton.Input active />
          </div>

          <div className={styles.settingsList}>
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className={styles.settingItem}>
                <div className={styles.settingContent}>
                  <Skeleton.Input active />
                </div>
                <div className={styles.switchWrapper}>
                  <Skeleton.Button active />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.notificationSettings}>
      <div className={styles.innerWrapper}>
        <div className={styles.header}>
          <h2 className={styles.title}>Notification Settings</h2>
        </div>

        <div className={styles.settingsList}>
          {NOTIFICATION_OPTIONS.map((option) => (
            <div key={option.key} className={styles.settingItem}>
              <div className={styles.settingContent}>
                <h3 className={styles.settingTitle}>{option.title}</h3>
                <p className={styles.settingDescription}>{option.description}</p>
              </div>
              <div className={styles.switchWrapper}>
                <Switch
                  checked={localSettings[option.key]}
                  onChange={(checked) => handleSettingChange(option.key, checked)}
                  className={styles.switch}
                  disabled={isLoadingSettings || pendingUpdates.has(option.key)}
                  loading={pendingUpdates.has(option.key)}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export { NotificationSettings };
