@use '../../../styles/media' as *;

.personalInfo {
  h2 {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
  }

  .innerWrapper {
    background-color: var(--neutral-600);
    border-radius: 8px;
  }

  > * {
    padding: 0 15px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-top: 15px;

    @include mobile {
      margin-bottom: 24px;
    }
  }

  .title {
    color: var(--white);
  }

  .saveButton {
    min-width: 80px;
  }

  .formRow {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 14px;
  }

  .form {
    padding-bottom: 20px;
  }

  label {
    cursor: pointer;
    color: var(--neutral-300);
    font-size: var(--font-size-xs);

    &::before {
      display: none;
    }
  }

  .formItem {
    margin-bottom: 20px;
  }

  .passwordSection {
    margin-top: 10px;
    padding: 15px 0;
  }

  .passwordHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .passwordTitle {
    color: var(--white);
  }

  .changePasswordButton {
    min-width: 140px;

    @include mobile {
      min-width: 120px;
      font-size: var(--font-size-xs);
    }
  }
}
