import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { User } from '../../api/authApi';

interface AuthState {
  user: User | null;
}

/**
 * TODO: Remove mock user when integrating with real backend
 */
// const initialState: AuthState = {
//   user: {
//     id: '1',
//     email: '<EMAIL>',
//     name: '<PERSON>',
//     surname: '<PERSON><PERSON><PERSON>',
//     username: 'l<PERSON><PERSON><PERSON><PERSON>',
//     birthday: '2000-12-22',
//     avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=John',
//     authorized: true,
//   },
// };

const initialState: AuthState = {
  user: null,
};

/**
 * Auth slice handles only user data and basic state management
 * All API calls, loading states, and errors are handled by RTK Query
 */
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout(state) {
      state.user = null;
    },

    setUser(state, action: PayloadAction<User>) {
      state.user = action.payload;
    },
  },
});

export const { logout, setUser } = authSlice.actions;
export default authSlice.reducer;
