import { createSlice } from '@reduxjs/toolkit';

interface ModalState {
  loginModalOpen: boolean;
  registerModalOpen: boolean;
  resetPasswordModalOpen: boolean;
}

const initialState: ModalState = {
  loginModalOpen: false,
  registerModalOpen: false,
  resetPasswordModalOpen: false,
};

const modalSlice = createSlice({
  name: 'modal',
  initialState,
  reducers: {
    openLoginModal(state) {
      state.loginModalOpen = true;
      state.registerModalOpen = false;
      state.resetPasswordModalOpen = false;
    },
    openRegisterModal(state) {
      state.registerModalOpen = true;
      state.loginModalOpen = false;
      state.resetPasswordModalOpen = false;
    },
    openResetPasswordModal(state) {
      state.resetPasswordModalOpen = true;
      state.loginModalOpen = false;
      state.registerModalOpen = false;
    },
    closeModals(state) {
      state.loginModalOpen = false;
      state.registerModalOpen = false;
      state.resetPasswordModalOpen = false;
    },
  },
});

export const { openLoginModal, openRegisterModal, openResetPasswordModal, closeModals } =
  modalSlice.actions;
export default modalSlice.reducer;
