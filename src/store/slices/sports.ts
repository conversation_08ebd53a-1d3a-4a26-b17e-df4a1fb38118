import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { SportType } from '../../types/entities.ts';

interface SportsState {
  activeSportType: SportType;
}

const initialState: SportsState = {
  activeSportType: SportType.FOOTBALL,
};

const sportsSlice = createSlice({
  name: 'sports',
  initialState,
  reducers: {
    setActiveSportType(state, action: PayloadAction<SportType>) {
      state.activeSportType = action.payload;
    },
  },
});

export const { setActiveSportType } = sportsSlice.actions;
export default sportsSlice.reducer;
