
//// Universal button styles for Ant Design
//.ant-btn,
//.ant-btn-default {
//  &:not(.ant-btn-link) {
//    width: auto;
//    border-radius: 8px;
//    height: 40px;
//    font-weight: 500;
//    padding: 0 22px;
//    font-size: var(--font-size-sm);
//  }
//}
//
//.ant-btn-primary,
//.ant-btn-secondary {
//  background-color: var(--neutral-500);
//  border-color: var(--neutral-500);
//  color: var(--neutral-400);
//}
//
//.ant-btn-secondary {
//  color: var(--white);
//}
//
//.ant-btn-primary {
//  background-color: var(--primary-500);
//  border-color: var(--primary-500);
//  box-shadow: 0px -3px 0px 0px rgba(0, 0, 0, 0.25) inset;
//  color: var(--black);
//  transition: all 0.1s ease-in-out;
//  transform: scale(1);
//
//  &:hover:not(:disabled) {
//    background-color: var(--primary-600);
//    border-color: var(--primary-600);
//  }
//
//  &:active:not(:disabled) {
//    transform: scale(0.98);
//    box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.4) inset;
//  }
//
//  &:disabled {
//    background-color: var(--neutral-500);
//    border-color: var(--neutral-500);
//    color: var(--neutral-400);
//    cursor: not-allowed;
//    font-weight: 600;
//  }
//}
//
//.ant-btn-variant-link,
//.ant-btn-link {
//  color: var(--primary-500);
//
//  &:hover:not(:disabled) {
//    color: var(--primary-600);
//  }
//
//  &:disabled {
//    color: var(--neutral-400);
//  }
//}

