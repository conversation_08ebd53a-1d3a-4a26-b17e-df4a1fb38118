// Universal dropdown styles for Ant Design components
.ant-select-dropdown {
  background-color: var(--neutral-600);
  border: 1px solid var(--neutral-500);
  border-radius: 6px;

  .ant-select-item {
    color: var(--white);

    &:hover,
    &.ant-select-item-option-selected {
      background-color: var(--neutral-500);
      color: var(--primary-500);
    }
  }
}

// Date picker dropdown styles
.ant-picker-dropdown {
  .ant-picker-panel-container {
    background-color: var(--neutral-600);
    border: 1px solid var(--neutral-500);
    border-radius: 6px;
  }

  .ant-picker-panel {
    background-color: var(--neutral-600);
    border: none;
  }

  .ant-picker-header {
    border-bottom: 1px solid var(--neutral-500);

    button {
      color: var(--white);

      &:hover {
        color: var(--primary-500);
      }
    }
  }

  .ant-picker-body {
    th {
      color: var(--neutral-300);
    }
  }

  .ant-picker-cell {
    color: var(--white);

    &:hover .ant-picker-cell-inner {
      background-color: var(--neutral-500);
    }

    &.ant-picker-cell-selected .ant-picker-cell-inner {
      background-color: var(--primary-500);
      color: var(--black);
    }

    &.ant-picker-cell-today .ant-picker-cell-inner {
      border-color: var(--primary-500);
    }
  }
}
