@mixin mobile {
  @media (max-width: 767.98px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1439.98px) {
    @content;
  }
}

@mixin laptop {
  @media (min-width: 1440px) and (max-width: 1919.98px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1920px) {
    @content;
  }
}

// Grouped
@mixin above-mobile {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin above-tablet {
  @media (min-width: 1440px) {
    @content;
  }
}

@mixin above-laptop {
  @media (min-width: 1920px) {
    @content;
  }
}

@mixin below-tablet {
  @media (max-width: 1439.98px) {
    @content;
  }
}
