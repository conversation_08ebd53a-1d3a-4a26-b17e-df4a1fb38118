// Typography styles based on design system

// Text styles
.text-tiny {
  font-size: var(--font-size-xss);
  font-weight: var(--font-weight-normal);
}

.text-large {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
}

.text-lead {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
}

.text-list {
  font-size: var(--font-size-sm-md);
  font-weight: var(--font-weight-normal);
}

// Subtitle styles
.subtitle-regular {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
}

.subtitle-semibold {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
}

// Paragraph styles
.paragraph-regular {
  font-size: var(--font-size-sm-md);
  font-weight: var(--font-weight-normal);
}

.paragraph-semibold {
  font-size: var(--font-size-sm-md);
  font-weight: var(--font-weight-semibold);
}

// Small text styles
.small-medium {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.small-regular {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
}

.small-bold {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
}

// H5 styles
.h5-base {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
}

.h5-caps {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
}

// H4 styles
.h4-base {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-normal);
}

.h4-caps {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
}

// H3 styles
.h3-base {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-normal);
}

.h3-caps {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
}

// H2 styles
.h2-base {
  font-size: var(--font-size-heading);
  font-weight: var(--font-weight-normal);
}

.h2-caps {
  font-size: var(--font-size-heading);
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
}

// H1 styles
.h1-base {
  font-size: var(--font-size-heading-xl);
  font-weight: var(--font-weight-normal);
}

.h1-caps {
  font-size: var(--font-size-heading-xl);
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
}

// Semantic HTML elements with default styles
h1 {
  @extend .h1-base;
  margin: 0;
}

h2 {
  @extend .h2-base;
  margin: 0;
}

h3 {
  @extend .h3-base;
  margin: 0;
}

h4 {
  @extend .h4-base;
  margin: 0;
}

h5 {
  @extend .h5-base;
  margin: 0;
}

h6 {
  @extend .small-bold;
  margin: 0;
}

p {
  @extend .paragraph-regular;
  margin: 0;
}

// Utility classes for common text modifications
.text-bold {
  font-weight: var(--font-weight-bold);
}

.text-semibold {
  font-weight: var(--font-weight-semibold);
}

.text-medium {
  font-weight: var(--font-weight-medium);
}

.text-regular {
  font-weight: var(--font-weight-normal);
}

.text-uppercase {
  text-transform: uppercase;
}

.text-lowercase {
  text-transform: lowercase;
}

.text-capitalize {
  text-transform: capitalize;
}
