export enum SportType {
  FOOTBALL = 'Soccer',
  BASKETBALL = 'Basketball',
  AMERICAN_FOOTBALL = 'American Football',
  BASEBALL = 'Baseball',
  TABLE_TENNIS = 'Table Tennis',
  TENNIS = 'Tennis',
  CRICKET = 'Cricket',
  HANDBALL = 'Handball',
  VOLLEYBALL = 'Volleyball',
  ICE_HOCKEY = 'Ice Hockey',
  ESPORTS = 'Esports',
}

export enum COEF_DIRECTION {
  up = 'UP',
  down = 'DOWN',
  none = 'NONE',
}

export type GameEventT = {
  id: string;
  isLive: boolean;
  tournament: string; // "UEFA Champions League"
  stage?: string; // example: "1st set", or "Quarter-final"
  startDate: string; // ISO string

  teamOne: {
    name: string;
    logo: string;
    score: number;
  };
  teamTwo: {
    name: string;
    logo: string;
    score: number;
  };

  odds: BetOddT[];

  hasMoreMarkets?: boolean;
};

// Group of events e.g "UEFA Champions League"

export type GameLeague = {
  id: string;
  leagueName: string;
  gameCount: number;
  games: GameEventT[];
};

export enum ODD_TYPE {
  ONE = '1',
  DRAW = 'x',
  TWO = '2',
  OVER = 'over',
  TOTAL = 'total',
  UNDER = 'under',
}

export type BetOddT = {
  id?: number;
  value: number;
  direction: COEF_DIRECTION;
  isLocked: boolean;
  oddType?: ODD_TYPE;
};
