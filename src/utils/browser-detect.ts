export const isMobileDevice = (userAgent: string) => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
};

export const isiOSSafari = (userAgent: string) => {
  return /^((?!chrome|android).)*safari/i.test(userAgent);
};

export const isiOSChrome = (userAgent: string) => {
  return /CriOS/i.test(userAgent);
};

export const isAndroidChrome = (userAgent: string) => {
  return /Chrome/i.test(userAgent);
};
