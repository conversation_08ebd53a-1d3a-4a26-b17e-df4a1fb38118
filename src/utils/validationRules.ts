// Helper function to check username uniqueness (would be replaced with actual API call)
const checkUsernameUniqueness = async (username: string): Promise<boolean> => {
  // This is a mock function - replace with actual API call
  // For now, let's simulate that some usernames are taken
  const takenUsernames = ['admin', 'test', 'user123'];
  return !takenUsernames.includes(username.toLowerCase());
};

export const validationRules = {
  username: [
    { required: true, message: 'Username should be stated' },
    { max: 70, message: 'Please, state a valid username' },
    {
      pattern: /^[a-zA-Z0-9_.-]+$/,
      message: 'Please, state a valid username',
    },
    {
      validator: async (_: unknown, value: string) => {
        if (!value) return Promise.resolve();
        // This would be replaced with actual API call to check uniqueness
        // For now, simulating the check
        const isUnique = await checkUsernameUniqueness(value);
        if (!isUnique) {
          return Promise.reject(new Error('This username is already in use'));
        }
        return Promise.resolve();
      },
    },
  ],

  // Full name validation (combining name and surname)
  fullName: [
    { required: true, message: 'Full name should be stated' },
    { max: 50, message: 'Please, state a valid full name' },
    {
      pattern: /^[a-zA-Z\s]+$/,
      message: 'Please, state a valid full name',
    },
  ],

  name: [
    { required: true, message: 'Full name should be stated' },
    { max: 25, message: 'Please, state a valid full name' },
    {
      pattern: /^[a-zA-Z\s]+$/,
      message: 'Please, state a valid full name',
    },
  ],

  surname: [
    { required: true, message: 'Full name should be stated' },
    { max: 25, message: 'Please, state a valid full name' },
    {
      pattern: /^[a-zA-Z\s]+$/,
      message: 'Please, state a valid full name',
    },
  ],

  birthday: [
    { required: true, message: 'Birthday should be stated' },
    {
      validator: (_: unknown, value: unknown) => {
        if (!value) {
          return Promise.resolve();
        }

        // Handle Dayjs objects (from DatePicker) and regular Date objects
        let date: Date;
        if (typeof value === 'object' && value !== null && 'toDate' in value) {
          // Dayjs object
          date = (value as { toDate(): Date }).toDate();
        } else {
          // Assume it's a Date or date string
          date = new Date(value as string | Date);
        }

        const today = new Date();
        const birthYear = date.getFullYear();
        const currentYear = today.getFullYear();

        // Check if date is in the future
        if (date > today) {
          return Promise.reject(new Error('Please enter a valid date of birth'));
        }

        // Check if year is before 1900
        if (birthYear < 1900) {
          return Promise.reject(new Error('Please enter a valid date of birth'));
        }

        // Check if user is at least 18 years old
        const age = currentYear - birthYear;
        const monthDiff = today.getMonth() - date.getMonth();
        const dayDiff = today.getDate() - date.getDate();

        if (age < 18 || (age === 18 && (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)))) {
          return Promise.reject(new Error('You must be at least 18 years old to register'));
        }

        return Promise.resolve();
      },
    },
  ],

  email: [
    { required: true, message: 'Email should be stated' },
    {
      type: 'email' as const,
      message: 'Invalid email',
    },
  ],

  phone: [
    { required: true, message: 'Phone number should be stated' },
    {
      pattern: /^\+[1-9]\d{1,14}$/,
      message: 'Invalid phone number',
    },
  ],

  password: [
    { required: true, message: 'Password should be stated' },
    {
      min: 8,
      message:
        'Password should contain minimum 8 characters, at least 1 lower case letter and at least 1 higher case letter',
    },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z]).{8,}$/,
      message:
        'Password should contain minimum 8 characters, at least 1 lower case letter and at least 1 higher case letter',
    },
  ],

  confirmPassword: (getFieldValue: (name: string) => unknown) => [
    { required: true, message: 'Please confirm your password!' },
    {
      validator(_: unknown, value: string) {
        if (!value || getFieldValue('password') === value) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('Passwords do not match!'));
      },
    },
  ],

  privacyPolicy: [
    {
      validator(_: unknown, value: boolean) {
        if (value) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('You must accept the Privacy Policy!'));
      },
    },
  ],

  termsConditions: [
    {
      validator(_: unknown, value: boolean) {
        if (value) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('You must accept the Terms and Conditions!'));
      },
    },
  ],
};

export const stepFieldsMap = {
  1: ['username', 'name', 'surname', 'birthday'],
  2: ['email', 'phone', 'password'],
  3: ['privacyPolicy', 'termsConditions'],
} as const;
